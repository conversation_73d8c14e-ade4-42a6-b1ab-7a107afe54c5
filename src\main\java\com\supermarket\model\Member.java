package com.supermarket.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员实体类
 * 对应数据库members表
 */
public class Member {
    
    // 会员状态枚举
    public enum Status {
        ACTIVE("启用"),
        INACTIVE("停用");
        
        private final String description;
        
        Status(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    private Integer id;
    private String memberCode;           // 会员卡号
    private String name;                 // 会员姓名
    private String phone;                // 手机号码
    private String email;                // 邮箱
    private Integer levelId;             // 会员等级ID
    private Integer totalPoints;         // 总积分
    private Integer availablePoints;     // 可用积分
    private BigDecimal totalConsumption; // 累计消费金额
    private Status status;               // 会员状态
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 关联对象
    private MemberLevel memberLevel;     // 会员等级
    
    // 构造方法
    public Member() {
        this.totalPoints = 0;
        this.availablePoints = 0;
        this.totalConsumption = BigDecimal.ZERO;
        this.status = Status.ACTIVE;
        this.levelId = 1; // 默认普通会员
    }
    
    public Member(String memberCode, String name, String phone) {
        this();
        this.memberCode = memberCode;
        this.name = name;
        this.phone = phone;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getMemberCode() {
        return memberCode;
    }
    
    public void setMemberCode(String memberCode) {
        this.memberCode = memberCode;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public Integer getLevelId() {
        return levelId;
    }
    
    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }
    
    public Integer getTotalPoints() {
        return totalPoints;
    }
    
    public void setTotalPoints(Integer totalPoints) {
        this.totalPoints = totalPoints;
    }
    
    public Integer getAvailablePoints() {
        return availablePoints;
    }
    
    public void setAvailablePoints(Integer availablePoints) {
        this.availablePoints = availablePoints;
    }
    
    public BigDecimal getTotalConsumption() {
        return totalConsumption;
    }
    
    public void setTotalConsumption(BigDecimal totalConsumption) {
        this.totalConsumption = totalConsumption;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public void setStatus(Status status) {
        this.status = status;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public MemberLevel getMemberLevel() {
        return memberLevel;
    }
    
    public void setMemberLevel(MemberLevel memberLevel) {
        this.memberLevel = memberLevel;
    }
    
    @Override
    public String toString() {
        return "Member{" +
                "id=" + id +
                ", memberCode='" + memberCode + '\'' +
                ", name='" + name + '\'' +
                ", phone='" + phone + '\'' +
                ", levelId=" + levelId +
                ", totalPoints=" + totalPoints +
                ", availablePoints=" + availablePoints +
                ", totalConsumption=" + totalConsumption +
                ", status=" + status +
                '}';
    }
}
