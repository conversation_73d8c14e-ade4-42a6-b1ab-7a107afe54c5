package com.supermarket.model;

import java.time.LocalDateTime;

/**
 * 商品分类实体类
 * 对应数据库categories表
 */
public class Category {
    
    private Integer id;
    private String categoryCode;    // 分类编码
    private String categoryName;    // 分类名称
    private Integer parentId;       // 父分类ID
    private Integer level;          // 分类层级
    private LocalDateTime createdAt;
    
    // 构造方法
    public Category() {}
    
    public Category(String categoryCode, String categoryName, Integer level) {
        this.categoryCode = categoryCode;
        this.categoryName = categoryName;
        this.level = level;
    }
    
    public Category(String categoryCode, String categoryName, Integer parentId, Integer level) {
        this.categoryCode = categoryCode;
        this.categoryName = categoryName;
        this.parentId = parentId;
        this.level = level;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getCategoryCode() {
        return categoryCode;
    }
    
    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }
    
    public String getCategoryName() {
        return categoryName;
    }
    
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }
    
    public Integer getParentId() {
        return parentId;
    }
    
    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }
    
    public Integer getLevel() {
        return level;
    }
    
    public void setLevel(Integer level) {
        this.level = level;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "Category{" +
                "id=" + id +
                ", categoryCode='" + categoryCode + '\'' +
                ", categoryName='" + categoryName + '\'' +
                ", parentId=" + parentId +
                ", level=" + level +
                '}';
    }
}
