package com.supermarket.service;

import com.supermarket.config.DatabaseConfig;
import com.supermarket.model.Inventory;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 库存服务类
 * 负责库存管理相关的业务逻辑
 */
public class InventoryService {
    
    /**
     * 检查商品库存是否充足
     * @param productId 商品ID
     * @param requiredQuantity 需要的数量
     * @return 是否库存充足
     */
    public boolean checkStock(Integer productId, Integer requiredQuantity) {
        String sql = "SELECT SUM(quantity) as total_quantity FROM inventory WHERE product_id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, productId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    int totalQuantity = rs.getInt("total_quantity");
                    return totalQuantity >= requiredQuantity;
                }
            }
        } catch (SQLException e) {
            System.err.println("检查库存失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return false;
    }
    
    /**
     * 扣减库存
     * @param productId 商品ID
     * @param quantity 扣减数量
     * @param reason 扣减原因
     * @param operatorId 操作员ID
     * @return 是否扣减成功
     */
    public boolean reduceStock(Integer productId, Integer quantity, String reason, Integer operatorId) {
        Connection conn = null;
        try {
            conn = DatabaseConfig.getConnection();
            conn.setAutoCommit(false); // 开启事务
            
            // 查找有库存的货架，按数量从多到少排序
            String selectSql = "SELECT id, shelf_id, quantity FROM inventory " +
                              "WHERE product_id = ? AND quantity > 0 ORDER BY quantity DESC";
            
            List<Inventory> inventories = new ArrayList<>();
            try (PreparedStatement selectStmt = conn.prepareStatement(selectSql)) {
                selectStmt.setInt(1, productId);
                try (ResultSet rs = selectStmt.executeQuery()) {
                    while (rs.next()) {
                        Inventory inventory = new Inventory();
                        inventory.setId(rs.getInt("id"));
                        inventory.setShelfId(rs.getInt("shelf_id"));
                        inventory.setQuantity(rs.getInt("quantity"));
                        inventories.add(inventory);
                    }
                }
            }
            
            int remainingQuantity = quantity;
            
            // 从库存充足的货架开始扣减
            for (Inventory inventory : inventories) {
                if (remainingQuantity <= 0) {
                    break;
                }
                
                int currentQuantity = inventory.getQuantity();
                int deductQuantity = Math.min(remainingQuantity, currentQuantity);
                int newQuantity = currentQuantity - deductQuantity;
                
                // 更新库存
                String updateSql = "UPDATE inventory SET quantity = ? WHERE id = ?";
                try (PreparedStatement updateStmt = conn.prepareStatement(updateSql)) {
                    updateStmt.setInt(1, newQuantity);
                    updateStmt.setInt(2, inventory.getId());
                    updateStmt.executeUpdate();
                }
                
                // 记录库存变动日志
                String logSql = "INSERT INTO inventory_logs (shelf_id, product_id, change_type, " +
                               "quantity_before, quantity_change, quantity_after, reason, operator_id) " +
                               "VALUES (?, ?, 'OUT', ?, ?, ?, ?, ?)";
                try (PreparedStatement logStmt = conn.prepareStatement(logSql)) {
                    logStmt.setInt(1, inventory.getShelfId());
                    logStmt.setInt(2, productId);
                    logStmt.setInt(3, currentQuantity);
                    logStmt.setInt(4, -deductQuantity);
                    logStmt.setInt(5, newQuantity);
                    logStmt.setString(6, reason);
                    logStmt.setInt(7, operatorId);
                    logStmt.executeUpdate();
                }
                
                remainingQuantity -= deductQuantity;
            }
            
            if (remainingQuantity > 0) {
                conn.rollback();
                System.out.println("库存不足，无法完成扣减操作");
                return false;
            }
            
            conn.commit();
            
            // 检查是否需要补货提醒
            checkRestockAlert(productId);
            
            return true;
            
        } catch (SQLException e) {
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
            System.err.println("扣减库存失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }
    
    /**
     * 增加库存（补货）
     * @param shelfId 货架ID
     * @param productId 商品ID
     * @param quantity 增加数量
     * @param reason 增加原因
     * @param operatorId 操作员ID
     * @return 是否增加成功
     */
    public boolean addStock(Integer shelfId, Integer productId, Integer quantity, 
                           String reason, Integer operatorId) {
        Connection conn = null;
        try {
            conn = DatabaseConfig.getConnection();
            conn.setAutoCommit(false);
            
            // 查找该货架上是否已有该商品的库存记录
            String selectSql = "SELECT id, quantity FROM inventory WHERE shelf_id = ? AND product_id = ?";
            Integer inventoryId = null;
            int currentQuantity = 0;
            
            try (PreparedStatement selectStmt = conn.prepareStatement(selectSql)) {
                selectStmt.setInt(1, shelfId);
                selectStmt.setInt(2, productId);
                try (ResultSet rs = selectStmt.executeQuery()) {
                    if (rs.next()) {
                        inventoryId = rs.getInt("id");
                        currentQuantity = rs.getInt("quantity");
                    }
                }
            }
            
            int newQuantity = currentQuantity + quantity;
            
            if (inventoryId != null) {
                // 更新现有库存记录
                String updateSql = "UPDATE inventory SET quantity = ? WHERE id = ?";
                try (PreparedStatement updateStmt = conn.prepareStatement(updateSql)) {
                    updateStmt.setInt(1, newQuantity);
                    updateStmt.setInt(2, inventoryId);
                    updateStmt.executeUpdate();
                }
            } else {
                // 创建新的库存记录
                String insertSql = "INSERT INTO inventory (shelf_id, product_id, quantity) VALUES (?, ?, ?)";
                try (PreparedStatement insertStmt = conn.prepareStatement(insertSql)) {
                    insertStmt.setInt(1, shelfId);
                    insertStmt.setInt(2, productId);
                    insertStmt.setInt(3, quantity);
                    insertStmt.executeUpdate();
                }
            }
            
            // 记录库存变动日志
            String logSql = "INSERT INTO inventory_logs (shelf_id, product_id, change_type, " +
                           "quantity_before, quantity_change, quantity_after, reason, operator_id) " +
                           "VALUES (?, ?, 'IN', ?, ?, ?, ?, ?)";
            try (PreparedStatement logStmt = conn.prepareStatement(logSql)) {
                logStmt.setInt(1, shelfId);
                logStmt.setInt(2, productId);
                logStmt.setInt(3, currentQuantity);
                logStmt.setInt(4, quantity);
                logStmt.setInt(5, newQuantity);
                logStmt.setString(6, reason);
                logStmt.setInt(7, operatorId);
                logStmt.executeUpdate();
            }
            
            conn.commit();
            System.out.println("库存增加成功");
            return true;
            
        } catch (SQLException e) {
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
            System.err.println("增加库存失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }
    
    /**
     * 检查补货提醒
     * @param productId 商品ID
     */
    private void checkRestockAlert(Integer productId) {
        String sql = "SELECT i.*, p.product_name, s.shelf_code " +
                    "FROM inventory i " +
                    "LEFT JOIN products p ON i.product_id = p.id " +
                    "LEFT JOIN shelves s ON i.shelf_id = s.id " +
                    "WHERE i.product_id = ? AND i.quantity <= i.min_stock";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, productId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String productName = rs.getString("product_name");
                    String shelfCode = rs.getString("shelf_code");
                    int quantity = rs.getInt("quantity");
                    int minStock = rs.getInt("min_stock");
                    
                    System.out.println("【补货提醒】商品：" + productName + 
                                     "，货架：" + shelfCode + 
                                     "，当前库存：" + quantity + 
                                     "，警戒值：" + minStock);
                }
            }
        } catch (SQLException e) {
            System.err.println("检查补货提醒失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 查询所有需要补货的商品
     * @return 需要补货的库存列表
     */
    public List<Inventory> getRestockAlerts() {
        List<Inventory> alerts = new ArrayList<>();
        String sql = "SELECT i.*, p.product_name, s.shelf_code " +
                    "FROM inventory i " +
                    "LEFT JOIN products p ON i.product_id = p.id " +
                    "LEFT JOIN shelves s ON i.shelf_id = s.id " +
                    "WHERE i.quantity <= i.min_stock " +
                    "ORDER BY (i.quantity - i.min_stock) ASC";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                Inventory inventory = new Inventory();
                inventory.setId(rs.getInt("id"));
                inventory.setShelfId(rs.getInt("shelf_id"));
                inventory.setProductId(rs.getInt("product_id"));
                inventory.setQuantity(rs.getInt("quantity"));
                inventory.setMinStock(rs.getInt("min_stock"));
                alerts.add(inventory);
            }
        } catch (SQLException e) {
            System.err.println("查询补货提醒失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return alerts;
    }
}
