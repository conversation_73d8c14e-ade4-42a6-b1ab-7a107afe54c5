package com.supermarket.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品实体类
 * 对应数据库products表
 */
public class Product {
    
    // 商品状态枚举
    public enum Status {
        ON_SHELF("上架"),
        OFF_SHELF("下架");
        
        private final String description;
        
        Status(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    private Integer id;
    private String productCode;      // 商品编码/条码
    private String productName;      // 商品名称
    private Integer categoryId;      // 商品分类ID
    private BigDecimal price;        // 商品价格
    private BigDecimal discountRate; // 折扣率
    private Status status;           // 商品状态
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 关联对象
    private Category category;       // 商品分类
    
    // 构造方法
    public Product() {
        this.discountRate = BigDecimal.ONE; // 默认无折扣
        this.status = Status.ON_SHELF;      // 默认上架状态
    }
    
    public Product(String productCode, String productName, Integer categoryId, BigDecimal price) {
        this();
        this.productCode = productCode;
        this.productName = productName;
        this.categoryId = categoryId;
        this.price = price;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getProductCode() {
        return productCode;
    }
    
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public Integer getCategoryId() {
        return categoryId;
    }
    
    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public BigDecimal getDiscountRate() {
        return discountRate;
    }
    
    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public void setStatus(Status status) {
        this.status = status;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Category getCategory() {
        return category;
    }
    
    public void setCategory(Category category) {
        this.category = category;
    }
    
    /**
     * 计算实际销售价格（考虑折扣）
     * @return 实际销售价格
     */
    public BigDecimal getActualPrice() {
        return price.multiply(discountRate);
    }
    
    @Override
    public String toString() {
        return "Product{" +
                "id=" + id +
                ", productCode='" + productCode + '\'' +
                ", productName='" + productName + '\'' +
                ", price=" + price +
                ", discountRate=" + discountRate +
                ", status=" + status +
                '}';
    }
}
