package com.supermarket.model;

import java.time.LocalDateTime;

/**
 * 货架实体类
 * 对应数据库shelves表
 */
public class Shelf {
    
    // 货架状态枚举
    public enum Status {
        ACTIVE("启用"),
        INACTIVE("停用");
        
        private final String description;
        
        Status(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    private Integer id;
    private String shelfCode;        // 货架编码（区-架-层）
    private String zoneCode;         // 区域编码
    private String rackCode;         // 货架编码
    private String layerCode;        // 层编码
    private String description;      // 货架描述
    private Status status;           // 货架状态
    private LocalDateTime createdAt;
    
    // 构造方法
    public Shelf() {
        this.status = Status.ACTIVE;
    }
    
    public Shelf(String shelfCode, String zoneCode, String rackCode, String layerCode, String description) {
        this();
        this.shelfCode = shelfCode;
        this.zoneCode = zoneCode;
        this.rackCode = rackCode;
        this.layerCode = layerCode;
        this.description = description;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getShelfCode() {
        return shelfCode;
    }
    
    public void setShelfCode(String shelfCode) {
        this.shelfCode = shelfCode;
    }
    
    public String getZoneCode() {
        return zoneCode;
    }
    
    public void setZoneCode(String zoneCode) {
        this.zoneCode = zoneCode;
    }
    
    public String getRackCode() {
        return rackCode;
    }
    
    public void setRackCode(String rackCode) {
        this.rackCode = rackCode;
    }
    
    public String getLayerCode() {
        return layerCode;
    }
    
    public void setLayerCode(String layerCode) {
        this.layerCode = layerCode;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public void setStatus(Status status) {
        this.status = status;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "Shelf{" +
                "id=" + id +
                ", shelfCode='" + shelfCode + '\'' +
                ", zoneCode='" + zoneCode + '\'' +
                ", rackCode='" + rackCode + '\'' +
                ", layerCode='" + layerCode + '\'' +
                ", description='" + description + '\'' +
                ", status=" + status +
                '}';
    }
}
