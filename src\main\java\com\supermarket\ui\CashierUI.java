package com.supermarket.ui;

import com.supermarket.model.SalesOrder;
import com.supermarket.model.SalesOrderItem;
import com.supermarket.model.User;
import com.supermarket.service.CashierService;

import java.util.Arrays;
import java.util.List;
import java.util.Scanner;

/**
 * 收银界面
 * 负责收银相关功能的用户交互
 */
public class CashierUI {
    
    private Scanner scanner;
    private User currentUser;
    private CashierService cashierService;
    private SalesOrder currentOrder; // 当前正在处理的订单
    
    public CashierUI(Scanner scanner, User currentUser) {
        this.scanner = scanner;
        this.currentUser = currentUser;
        this.cashierService = new CashierService();
    }
    
    /**
     * 显示收银功能菜单
     */
    public void show() {
        while (true) {
            System.out.println("\n" + "=".repeat(40));
            System.out.println("           收银功能");
            System.out.println("=".repeat(40));
            System.out.println("1. 开启收银业务");
            System.out.println("2. 调出挂单");
            System.out.println("3. 查看挂单列表");
            System.out.println("0. 返回主菜单");
            System.out.print("请选择功能: ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    startCashier();
                    break;
                case "2":
                    resumeOrder();
                    break;
                case "3":
                    showSuspendedOrders();
                    break;
                case "0":
                    return;
                default:
                    System.out.println("无效的选项，请重新选择");
            }
        }
    }
    
    /**
     * 开启收银业务
     */
    private void startCashier() {
        currentOrder = cashierService.startCashier(currentUser.getId());
        if (currentOrder != null) {
            showCashierOperations();
        }
    }
    
    /**
     * 显示收银操作菜单
     */
    private void showCashierOperations() {
        while (currentOrder != null && 
               (currentOrder.getStatus() == SalesOrder.Status.PENDING || 
                currentOrder.getStatus() == SalesOrder.Status.SUSPENDED)) {
            
            System.out.println("\n" + "-".repeat(40));
            System.out.println("当前订单：" + currentOrder.getOrderNo());
            System.out.println("订单状态：" + currentOrder.getStatus().getDescription());
            System.out.println("应收金额：¥" + currentOrder.getFinalAmount());
            System.out.println("-".repeat(40));
            System.out.println("1. 扫描商品条码");
            System.out.println("2. 设置会员信息");
            System.out.println("3. 查看订单明细");
            System.out.println("4. 结账");
            System.out.println("5. 挂单");
            System.out.println("6. 撤单");
            System.out.println("0. 完成当前订单");
            System.out.print("请选择操作: ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    scanProduct();
                    break;
                case "2":
                    setMember();
                    break;
                case "3":
                    showOrderDetails();
                    break;
                case "4":
                    checkout();
                    break;
                case "5":
                    suspendOrder();
                    break;
                case "6":
                    cancelOrder();
                    break;
                case "0":
                    currentOrder = null;
                    return;
                default:
                    System.out.println("无效的选项，请重新选择");
            }
        }
    }
    
    /**
     * 扫描商品条码
     */
    private void scanProduct() {
        System.out.print("请输入商品条码: ");
        String productCode = scanner.nextLine().trim();
        
        if (productCode.isEmpty()) {
            System.out.println("商品条码不能为空");
            return;
        }
        
        System.out.print("请输入商品数量 (默认1): ");
        String quantityStr = scanner.nextLine().trim();
        
        Integer quantity = 1;
        if (!quantityStr.isEmpty()) {
            try {
                quantity = Integer.parseInt(quantityStr);
                if (quantity <= 0) {
                    System.out.println("商品数量必须大于0");
                    return;
                }
            } catch (NumberFormatException e) {
                System.out.println("请输入有效的数量");
                return;
            }
        }
        
        boolean success = cashierService.addProduct(currentOrder, productCode, quantity);
        if (success) {
            // 刷新当前订单信息
            refreshCurrentOrder();
        }
    }
    
    /**
     * 设置会员信息
     */
    private void setMember() {
        System.out.print("请输入会员卡号: ");
        String memberCode = scanner.nextLine().trim();
        
        if (memberCode.isEmpty()) {
            System.out.println("会员卡号不能为空");
            return;
        }
        
        boolean success = cashierService.setMember(currentOrder, memberCode);
        if (success) {
            // 刷新当前订单信息
            refreshCurrentOrder();
        }
    }
    
    /**
     * 查看订单明细
     */
    private void showOrderDetails() {
        List<SalesOrderItem> items = cashierService.getOrderItems(currentOrder.getId());
        
        if (items.isEmpty()) {
            System.out.println("当前订单暂无商品");
            return;
        }
        
        System.out.println("\n--- 订单明细 ---");
        System.out.printf("%-20s %8s %8s %10s%n", "商品名称", "数量", "单价", "小计");
        System.out.println("-".repeat(50));
        
        for (SalesOrderItem item : items) {
            if (item.getProduct() != null) {
                System.out.printf("%-20s %8d %8.2f %10.2f%n",
                    item.getProduct().getProductName(),
                    item.getQuantity(),
                    item.getUnitPrice(),
                    item.getSubtotal());
            }
        }
        
        System.out.println("-".repeat(50));
        System.out.printf("应收总额：%32.2f%n", currentOrder.getTotalAmount());
        if (currentOrder.getDiscountAmount().compareTo(java.math.BigDecimal.ZERO) > 0) {
            System.out.printf("会员优惠：%32.2f%n", currentOrder.getDiscountAmount());
        }
        System.out.printf("实收金额：%32.2f%n", currentOrder.getFinalAmount());
    }
    
    /**
     * 结账
     */
    private void checkout() {
        if (currentOrder.getFinalAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            System.out.println("订单金额为0，无需结账");
            return;
        }
        
        System.out.println("\n--- 结账 ---");
        System.out.println("实收金额：¥" + currentOrder.getFinalAmount());
        System.out.println("支持的支付方式：");
        System.out.println("1. 现金");
        System.out.println("2. 银行卡");
        System.out.println("3. 赠券");
        System.out.println("4. 混合支付");
        System.out.print("请选择支付方式: ");
        
        String paymentChoice = scanner.nextLine().trim();
        List<String> paymentMethods;
        
        switch (paymentChoice) {
            case "1":
                paymentMethods = Arrays.asList("现金");
                break;
            case "2":
                paymentMethods = Arrays.asList("银行卡");
                break;
            case "3":
                paymentMethods = Arrays.asList("赠券");
                break;
            case "4":
                paymentMethods = Arrays.asList("现金", "银行卡", "赠券");
                break;
            default:
                System.out.println("无效的支付方式");
                return;
        }
        
        System.out.print("确认结账？(y/n): ");
        String confirm = scanner.nextLine().trim().toLowerCase();
        
        if (confirm.equals("y") || confirm.equals("yes")) {
            boolean success = cashierService.checkout(currentOrder, paymentMethods);
            if (success) {
                currentOrder = null; // 结账成功，清空当前订单
            }
        } else {
            System.out.println("已取消结账");
        }
    }
    
    /**
     * 挂单
     */
    private void suspendOrder() {
        System.out.print("确认挂单？(y/n): ");
        String confirm = scanner.nextLine().trim().toLowerCase();
        
        if (confirm.equals("y") || confirm.equals("yes")) {
            boolean success = cashierService.suspendOrder(currentOrder);
            if (success) {
                currentOrder = null; // 挂单成功，清空当前订单
            }
        } else {
            System.out.println("已取消挂单");
        }
    }
    
    /**
     * 撤单
     */
    private void cancelOrder() {
        System.out.print("确认撤单？撤单后订单将永久保存但无法恢复 (y/n): ");
        String confirm = scanner.nextLine().trim().toLowerCase();
        
        if (confirm.equals("y") || confirm.equals("yes")) {
            boolean success = cashierService.cancelOrder(currentOrder);
            if (success) {
                currentOrder = null; // 撤单成功，清空当前订单
            }
        } else {
            System.out.println("已取消撤单");
        }
    }
    
    /**
     * 调出挂单
     */
    private void resumeOrder() {
        System.out.print("请输入要调出的销售单号: ");
        String orderNo = scanner.nextLine().trim();
        
        if (orderNo.isEmpty()) {
            System.out.println("销售单号不能为空");
            return;
        }
        
        SalesOrder order = cashierService.resumeOrder(orderNo);
        if (order != null) {
            currentOrder = order;
            showCashierOperations();
        }
    }
    
    /**
     * 查看挂单列表
     */
    private void showSuspendedOrders() {
        List<SalesOrder> suspendedOrders = cashierService.getSuspendedOrders();
        
        if (suspendedOrders.isEmpty()) {
            System.out.println("当前没有挂单");
            return;
        }
        
        System.out.println("\n--- 挂单列表 ---");
        System.out.printf("%-15s %-10s %-15s %10s%n", "销售单号", "收银员", "会员", "金额");
        System.out.println("-".repeat(60));
        
        for (SalesOrder order : suspendedOrders) {
            String cashierName = order.getCashier() != null ? order.getCashier().getRealName() : "";
            String memberName = order.getMember() != null ? order.getMember().getName() : "非会员";
            
            System.out.printf("%-15s %-10s %-15s %10.2f%n",
                order.getOrderNo(),
                cashierName,
                memberName,
                order.getFinalAmount());
        }
    }
    
    /**
     * 刷新当前订单信息
     */
    private void refreshCurrentOrder() {
        // 这里可以重新从数据库获取订单信息，确保数据是最新的
        // 为简化实现，暂时保持当前对象
    }
}
