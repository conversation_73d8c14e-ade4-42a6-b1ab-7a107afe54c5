package com.supermarket;

import com.supermarket.config.DatabaseConfig;
import com.supermarket.ui.LoginUI;

/**
 * 超市前台销售系统主程序入口
 * 
 * 系统功能说明：
 * 1. 收银功能（收银员角色）
 *    - 开启收银业务，生成唯一销售单号
 *    - 扫描商品条码，确定商品价格和应收款
 *    - 支持会员消费积分及优惠折扣
 *    - 多种结账方式（现金、银行卡、赠券等）
 *    - 挂单、调单、撤单操作
 *    - 打印销售小票
 * 
 * 2. 商品管理功能（商品管理员角色）
 *    - 商品分类管理（线分类法）
 *    - 商品上架、移架、下架操作
 *    - 商品定价和折扣设置
 *    - 库存管理和补货提醒
 * 
 * 3. 会员管理功能（系统管理员角色）
 *    - 会员等级和规则维护
 *    - 新会员注册和信息维护
 *    - 会员积分管理
 *    - 会员升级处理
 * 
 * 4. 用户管理功能（系统管理员角色）
 *    - 用户账号添加、停用/启用
 *    - 密码重置和修改
 *    - 角色权限管理
 * 
 * 技术栈：Java 8 + MySQL 8 + 控制台界面
 */
public class Main {
    
    public static void main(String[] args) {
        System.out.println("正在启动超市前台销售系统...");
        
        // 测试数据库连接
        if (!testDatabaseConnection()) {
            System.err.println("数据库连接失败，系统无法启动");
            System.err.println("请检查以下配置：");
            System.err.println("1. MySQL服务是否已启动");
            System.err.println("2. 数据库连接配置是否正确（DatabaseConfig.java）");
            System.err.println("3. 数据库和表是否已创建（执行database.sql）");
            return;
        }
        
        System.out.println("数据库连接成功");
        System.out.println("系统启动完成");
        
        // 显示系统信息
        showSystemInfo();
        
        // 启动登录界面
        LoginUI loginUI = new LoginUI();
        loginUI.showLogin();
        
        System.out.println("系统已退出，感谢使用！");
    }
    
    /**
     * 测试数据库连接
     * @return 连接是否成功
     */
    private static boolean testDatabaseConnection() {
        try {
            return DatabaseConfig.testConnection();
        } catch (Exception e) {
            System.err.println("数据库连接测试异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 显示系统信息
     */
    private static void showSystemInfo() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("                超市前台销售系统 v1.0");
        System.out.println("=".repeat(60));
        System.out.println("技术栈：Java 8 + MySQL 8 + 控制台界面");
        System.out.println();
        System.out.println("系统角色及功能权限：");
        System.out.println("┌─────────────┬─────────────────────────────────────┐");
        System.out.println("│ 角色        │ 功能权限                            │");
        System.out.println("├─────────────┼─────────────────────────────────────┤");
        System.out.println("│ 系统管理员  │ 所有功能                            │");
        System.out.println("│ 商品管理员  │ 商品管理、数据查询、销售统计        │");
        System.out.println("│ 收银员      │ 收银功能                            │");
        System.out.println("│ 会员        │ 会员信息查询                        │");
        System.out.println("└─────────────┴─────────────────────────────────────┘");
        System.out.println();
        System.out.println("默认管理员账号：");
        System.out.println("  用户名: admin     密码: 123  (系统管理员)");
        System.out.println("  用户名: manager   密码: 123  (商品管理员)");
        System.out.println("  用户名: cashier   密码: 123  (收银员)");
        System.out.println();
        System.out.println("注意事项：");
        System.out.println("1. 首次使用请确保已执行database.sql创建数据库和表");
        System.out.println("2. 用户名必须是全英文，不能重复");
        System.out.println("3. 用户初始密码为123，建议首次登录后修改");
        System.out.println("4. 撤单后订单永久保存但无法恢复，请谨慎操作");
        System.out.println("=".repeat(60));
    }
}
