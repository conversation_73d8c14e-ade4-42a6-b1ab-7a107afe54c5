使用java8+mysql8+控制台实现如下系统 最简单话实现 只是个小demo 满足如下要求就行 下面有的都要实现好 表结构设计好 没有的不要自己扩展 一定要满足我的需求严格对应好如下需求 加好中文注释 代码注释都简洁易懂一些
超市前台销售
1．收银 (用户角色：收银员)
(1)开启收银业务。收银员开启收银业务，系统产生唯一的销售单号；
(2)确定应收款。获得商品编码，并据此确定商品价格，计算应收款。
收银员输入商品条码调入商品定价资料、输入商品数量(默认1)
计算商品应收款，汇总订单各商品应收款，计算应收款总额。顾
客可选择以会员身份购买，录入会员卡号，系统支持会员消费积
分及优惠折扣（规则在会员管理模块制定）。
(3)结账。收银员发出指令开启结账活动。支持多种结账方式 (现金
银行卡、赠券等）。结账后保存交易记录并打印销售小票。三
(4)挂单。收银员可以对销售单进行挂单、调单等操作。
(5)撤单。对错误的单子进行撤单，但单子永久保存，并正确设置状态
(6)打印销售小票。
2．商品管理（用户角色：商品管理员）
(1)商品分类。按照“线分类法"对商品进行分类管理。参考《百度百
科》词条：商品分类。
(2)商品上架。建立并维护超市的分区和货架(按*区-架-层'编码），并对
货架上商品建立库存账（记录每个货架上商品的收入和支出，据
此计算当前架上的每种商品的数量）。可对商品进行上架、移架、
下架（不再销售）操作，并建立完整的备查档案（记录商品每次
货架变化的前后状态）。
(3)商品定价。商品上架时完成定价，在架期间可调整定价，或设定
打折优惠规则。要设计一种运行机制保证货架上商品价格标牌与
系统中的价格绝对一致。
(4)补货。每发生商品销售业务导致上架商品数量减少时，系统根据
预先设定的商品在架商品的数量警戒值，发出提醒通知用户及时
上架补货。
3.会员管理（用户角色：系统管理员）
(1)维护会员运行规则。会员级别、升级规则、折扣规则等。
2)注册新会员。会员基本信息录入及维护，会员卡（号）发放。
(3）会员积分。会员每次购买商品结账后，系统要及时记录消费金额，
并按照规则计算积分。
(4）会员升级。当积分达到会员升级点时，系统提请用户确定会员是
否可以升级，确认后完成会员升级，并扣除相应积分。
4.用户管理（用户角色：系统管理员）
系统管理员负责用户账号的添加、停用/启用、复位密码。各用户登录
后可修改自己的登陆密码，修改前需要先提供原始密码。账号全英文，不
能重复，用户初始密码123。用户角色分为四类：系统管理员、商品管理
员、收银员、会员。角色功能分配如下表：
序号 角色 系统功能
1 系统管理员 ALL
2 商品管理员 商品管理，数据查询、销售统计
3 收银员 收银
4 会员 会员信息