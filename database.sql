-- 超市前台销售系统数据库设计
-- 使用MySQL 8.0

-- 创建数据库
CREATE DATABASE IF NOT EXISTS supermarket_pos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE supermarket_pos;

-- 1. 用户表 - 存储系统用户信息（系统管理员、商品管理员、收银员、会员）
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名（全英文）',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    role ENUM('ADMIN', 'PRODUCT_MANAGER', 'CASHIER', 'MEMBER') NOT NULL COMMENT '用户角色',
    real_name VARCHAR(100) COMMENT '真实姓名',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '账号状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '用户表';

-- 2. 商品分类表 - 线分类法
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_code VARCHAR(20) NOT NULL UNIQUE COMMENT '分类编码',
    category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
    parent_id INT COMMENT '父分类ID',
    level INT NOT NULL DEFAULT 1 COMMENT '分类层级',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id)
) COMMENT '商品分类表';

-- 3. 货架表 - 区-架-层编码
CREATE TABLE shelves (
    id INT PRIMARY KEY AUTO_INCREMENT,
    shelf_code VARCHAR(20) NOT NULL UNIQUE COMMENT '货架编码（区-架-层）',
    zone_code VARCHAR(10) NOT NULL COMMENT '区域编码',
    rack_code VARCHAR(10) NOT NULL COMMENT '货架编码',
    layer_code VARCHAR(10) NOT NULL COMMENT '层编码',
    description VARCHAR(200) COMMENT '货架描述',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '货架状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) COMMENT '货架表';

-- 4. 商品表
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_code VARCHAR(50) NOT NULL UNIQUE COMMENT '商品编码/条码',
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    category_id INT NOT NULL COMMENT '商品分类ID',
    price DECIMAL(10,2) NOT NULL COMMENT '商品价格',
    discount_rate DECIMAL(3,2) DEFAULT 1.00 COMMENT '折扣率（1.00表示无折扣）',
    status ENUM('ON_SHELF', 'OFF_SHELF') DEFAULT 'ON_SHELF' COMMENT '商品状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id)
) COMMENT '商品表';

-- 5. 库存表 - 记录每个货架上商品的收入和支出
CREATE TABLE inventory (
    id INT PRIMARY KEY AUTO_INCREMENT,
    shelf_id INT NOT NULL COMMENT '货架ID',
    product_id INT NOT NULL COMMENT '商品ID',
    quantity INT NOT NULL DEFAULT 0 COMMENT '当前库存数量',
    min_stock INT DEFAULT 10 COMMENT '最小库存警戒值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (shelf_id) REFERENCES shelves(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    UNIQUE KEY uk_shelf_product (shelf_id, product_id)
) COMMENT '库存表';

-- 6. 库存变动记录表 - 商品每次货架变化的前后状态
CREATE TABLE inventory_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    shelf_id INT NOT NULL COMMENT '货架ID',
    product_id INT NOT NULL COMMENT '商品ID',
    change_type ENUM('IN', 'OUT', 'MOVE', 'ADJUST') NOT NULL COMMENT '变动类型',
    quantity_before INT NOT NULL COMMENT '变动前数量',
    quantity_change INT NOT NULL COMMENT '变动数量',
    quantity_after INT NOT NULL COMMENT '变动后数量',
    reason VARCHAR(200) COMMENT '变动原因',
    operator_id INT COMMENT '操作员ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (shelf_id) REFERENCES shelves(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (operator_id) REFERENCES users(id)
) COMMENT '库存变动记录表';

-- 7. 会员等级表
CREATE TABLE member_levels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    level_name VARCHAR(50) NOT NULL COMMENT '等级名称',
    min_points INT NOT NULL COMMENT '升级所需最小积分',
    discount_rate DECIMAL(3,2) NOT NULL DEFAULT 1.00 COMMENT '折扣率',
    points_rate DECIMAL(3,2) NOT NULL DEFAULT 0.01 COMMENT '积分比例（每元消费获得积分）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) COMMENT '会员等级表';

-- 8. 会员表
CREATE TABLE members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    member_code VARCHAR(20) NOT NULL UNIQUE COMMENT '会员卡号',
    name VARCHAR(100) NOT NULL COMMENT '会员姓名',
    phone VARCHAR(20) COMMENT '手机号码',
    email VARCHAR(100) COMMENT '邮箱',
    level_id INT NOT NULL DEFAULT 1 COMMENT '会员等级ID',
    total_points INT DEFAULT 0 COMMENT '总积分',
    available_points INT DEFAULT 0 COMMENT '可用积分',
    total_consumption DECIMAL(10,2) DEFAULT 0.00 COMMENT '累计消费金额',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '会员状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (level_id) REFERENCES member_levels(id)
) COMMENT '会员表';

-- 9. 销售单表
CREATE TABLE sales_orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(30) NOT NULL UNIQUE COMMENT '销售单号',
    member_id INT COMMENT '会员ID（可为空）',
    cashier_id INT NOT NULL COMMENT '收银员ID',
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '应收总金额',
    discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '折扣金额',
    final_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '实收金额',
    status ENUM('PENDING', 'SUSPENDED', 'COMPLETED', 'CANCELLED') DEFAULT 'PENDING' COMMENT '订单状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id),
    FOREIGN KEY (cashier_id) REFERENCES users(id)
) COMMENT '销售单表';

-- 10. 销售单明细表
CREATE TABLE sales_order_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL COMMENT '销售单ID',
    product_id INT NOT NULL COMMENT '商品ID',
    quantity INT NOT NULL COMMENT '商品数量',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    discount_rate DECIMAL(3,2) DEFAULT 1.00 COMMENT '折扣率',
    subtotal DECIMAL(10,2) NOT NULL COMMENT '小计金额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES sales_orders(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
) COMMENT '销售单明细表';

-- 11. 交易记录表 - 结账交易记录
CREATE TABLE transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL COMMENT '销售单ID',
    payment_method ENUM('CASH', 'CARD', 'VOUCHER') NOT NULL COMMENT '支付方式',
    amount DECIMAL(10,2) NOT NULL COMMENT '支付金额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES sales_orders(id)
) COMMENT '交易记录表';

-- 12. 积分记录表
CREATE TABLE points_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    member_id INT NOT NULL COMMENT '会员ID',
    order_id INT COMMENT '关联销售单ID',
    points_change INT NOT NULL COMMENT '积分变动（正数为增加，负数为扣除）',
    points_type ENUM('EARN', 'REDEEM', 'UPGRADE', 'ADJUST') NOT NULL COMMENT '积分类型',
    description VARCHAR(200) COMMENT '积分变动描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id),
    FOREIGN KEY (order_id) REFERENCES sales_orders(id)
) COMMENT '积分记录表';

-- 插入初始数据

-- 插入默认会员等级
INSERT INTO member_levels (level_name, min_points, discount_rate, points_rate) VALUES
('普通会员', 0, 1.00, 0.01),
('银卡会员', 1000, 0.95, 0.015),
('金卡会员', 5000, 0.90, 0.02),
('钻石会员', 10000, 0.85, 0.025);

-- 插入默认管理员用户
INSERT INTO users (username, password, role, real_name) VALUES
('admin', '123', 'ADMIN', '系统管理员'),
('manager', '123', 'PRODUCT_MANAGER', '商品管理员'),
('cashier', '123', 'CASHIER', '收银员');

-- 插入示例商品分类
INSERT INTO categories (category_code, category_name, level) VALUES
('01', '食品饮料', 1),
('02', '日用百货', 1),
('03', '服装鞋帽', 1);

INSERT INTO categories (category_code, category_name, parent_id, level) VALUES
('0101', '休闲食品', 1, 2),
('0102', '饮料', 1, 2),
('0201', '洗护用品', 2, 2),
('0202', '厨房用品', 2, 2);

-- 插入示例货架
INSERT INTO shelves (shelf_code, zone_code, rack_code, layer_code, description) VALUES
('A-01-1', 'A', '01', '1', 'A区1号货架第1层'),
('A-01-2', 'A', '01', '2', 'A区1号货架第2层'),
('A-02-1', 'A', '02', '1', 'A区2号货架第1层'),
('B-01-1', 'B', '01', '1', 'B区1号货架第1层');

-- 插入示例商品
INSERT INTO products (product_code, product_name, category_id, price) VALUES
('6901234567890', '可口可乐 330ml', 2, 3.50),
('6901234567891', '薯片 大包装', 1, 8.90),
('6901234567892', '洗发水 500ml', 3, 25.80),
('6901234567893', '牙刷', 3, 12.50);

-- 插入示例库存
INSERT INTO inventory (shelf_id, product_id, quantity, min_stock) VALUES
(1, 1, 50, 10),
(2, 2, 30, 5),
(3, 3, 20, 8),
(4, 4, 40, 10);
