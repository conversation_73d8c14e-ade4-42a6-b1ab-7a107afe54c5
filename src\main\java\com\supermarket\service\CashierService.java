package com.supermarket.service;

import com.supermarket.dao.MemberDao;
import com.supermarket.dao.ProductDao;
import com.supermarket.dao.SalesOrderDao;
import com.supermarket.model.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 收银服务类
 * 负责收银相关的业务逻辑
 */
public class CashierService {
    
    private SalesOrderDao salesOrderDao;
    private ProductDao productDao;
    private MemberDao memberDao;
    private InventoryService inventoryService;
    
    public CashierService() {
        this.salesOrderDao = new SalesOrderDao();
        this.productDao = new ProductDao();
        this.memberDao = new MemberDao();
        this.inventoryService = new InventoryService();
    }
    
    /**
     * 开启收银业务，生成唯一销售单号
     * @param cashierId 收银员ID
     * @return 新的销售单对象
     */
    public SalesOrder startCashier(Integer cashierId) {
        // 生成唯一销售单号：格式为 SO + 年月日时分秒 + 3位随机数
        String orderNo = generateOrderNo();
        
        SalesOrder order = new SalesOrder(orderNo, cashierId);
        Integer orderId = salesOrderDao.insert(order);
        
        if (orderId != null) {
            order.setId(orderId);
            System.out.println("收银业务已开启，销售单号：" + orderNo);
            return order;
        } else {
            System.out.println("开启收银业务失败");
            return null;
        }
    }
    
    /**
     * 添加商品到销售单
     * @param order 销售单
     * @param productCode 商品条码
     * @param quantity 商品数量（默认1）
     * @return 是否添加成功
     */
    public boolean addProduct(SalesOrder order, String productCode, Integer quantity) {
        if (quantity == null || quantity <= 0) {
            quantity = 1; // 默认数量为1
        }
        
        // 根据商品条码查询商品信息
        Product product = productDao.findByProductCode(productCode);
        if (product == null) {
            System.out.println("商品不存在或已下架：" + productCode);
            return false;
        }
        
        // 检查库存
        if (!inventoryService.checkStock(product.getId(), quantity)) {
            System.out.println("库存不足，当前商品：" + product.getProductName());
            return false;
        }
        
        // 计算商品价格（考虑商品折扣）
        BigDecimal unitPrice = product.getActualPrice();
        
        // 创建销售单明细
        SalesOrderItem item = new SalesOrderItem(order.getId(), product.getId(), quantity, unitPrice);
        item.setDiscountRate(product.getDiscountRate());
        item.calculateSubtotal();
        
        // 保存销售单明细
        boolean success = salesOrderDao.insertOrderItem(item);
        if (success) {
            // 更新销售单总金额
            updateOrderAmount(order);
            System.out.println("商品添加成功：" + product.getProductName() + 
                             " x " + quantity + " = ¥" + item.getSubtotal());
            return true;
        } else {
            System.out.println("添加商品失败");
            return false;
        }
    }
    
    /**
     * 设置会员信息
     * @param order 销售单
     * @param memberCode 会员卡号
     * @return 是否设置成功
     */
    public boolean setMember(SalesOrder order, String memberCode) {
        Member member = memberDao.findByMemberCode(memberCode);
        if (member == null) {
            System.out.println("会员不存在或已停用：" + memberCode);
            return false;
        }
        
        order.setMemberId(member.getId());
        order.setMember(member);
        
        // 重新计算订单金额（应用会员折扣）
        updateOrderAmount(order);
        
        System.out.println("会员信息已设置：" + member.getName() + 
                         " (" + member.getMemberLevel().getLevelName() + ")");
        return true;
    }
    
    /**
     * 挂单操作
     * @param order 销售单
     * @return 是否挂单成功
     */
    public boolean suspendOrder(SalesOrder order) {
        order.setStatus(SalesOrder.Status.SUSPENDED);
        boolean success = salesOrderDao.updateStatus(order.getId(), SalesOrder.Status.SUSPENDED);
        
        if (success) {
            System.out.println("订单已挂单：" + order.getOrderNo());
        } else {
            System.out.println("挂单失败");
        }
        
        return success;
    }
    
    /**
     * 调单操作（恢复挂单）
     * @param orderNo 销售单号
     * @return 销售单对象，如果不存在返回null
     */
    public SalesOrder resumeOrder(String orderNo) {
        SalesOrder order = salesOrderDao.findByOrderNo(orderNo);
        if (order == null) {
            System.out.println("销售单不存在：" + orderNo);
            return null;
        }
        
        if (order.getStatus() != SalesOrder.Status.SUSPENDED) {
            System.out.println("该销售单不是挂单状态：" + orderNo);
            return null;
        }
        
        // 恢复为待结账状态
        order.setStatus(SalesOrder.Status.PENDING);
        boolean success = salesOrderDao.updateStatus(order.getId(), SalesOrder.Status.PENDING);
        
        if (success) {
            System.out.println("订单已调出：" + orderNo);
            return order;
        } else {
            System.out.println("调单失败");
            return null;
        }
    }
    
    /**
     * 撤单操作
     * @param order 销售单
     * @return 是否撤单成功
     */
    public boolean cancelOrder(SalesOrder order) {
        order.setStatus(SalesOrder.Status.CANCELLED);
        boolean success = salesOrderDao.updateStatus(order.getId(), SalesOrder.Status.CANCELLED);
        
        if (success) {
            System.out.println("订单已撤单：" + order.getOrderNo() + "（单子永久保存）");
        } else {
            System.out.println("撤单失败");
        }
        
        return success;
    }
    
    /**
     * 结账操作
     * @param order 销售单
     * @param paymentMethods 支付方式列表（现金、银行卡、赠券等）
     * @return 是否结账成功
     */
    public boolean checkout(SalesOrder order, List<String> paymentMethods) {
        if (order.getFinalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            System.out.println("订单金额为0，无需结账");
            return false;
        }
        
        // 更新订单状态为已完成
        order.setStatus(SalesOrder.Status.COMPLETED);
        boolean success = salesOrderDao.updateStatus(order.getId(), SalesOrder.Status.COMPLETED);
        
        if (success) {
            // 扣减库存
            List<SalesOrderItem> items = salesOrderDao.findOrderItems(order.getId());
            for (SalesOrderItem item : items) {
                inventoryService.reduceStock(item.getProductId(), item.getQuantity(), 
                                           "销售出库", order.getCashierId());
            }
            
            // 处理会员积分
            if (order.getMemberId() != null) {
                processMemberPoints(order);
            }
            
            // 打印销售小票
            printReceipt(order);
            
            System.out.println("结账成功！");
            return true;
        } else {
            System.out.println("结账失败");
            return false;
        }
    }
    
    /**
     * 查询挂单列表
     * @return 挂单列表
     */
    public List<SalesOrder> getSuspendedOrders() {
        return salesOrderDao.findSuspendedOrders();
    }
    
    /**
     * 查询销售单明细
     * @param orderId 销售单ID
     * @return 销售单明细列表
     */
    public List<SalesOrderItem> getOrderItems(Integer orderId) {
        return salesOrderDao.findOrderItems(orderId);
    }
    
    /**
     * 生成唯一销售单号
     * @return 销售单号
     */
    private String generateOrderNo() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        int random = (int) (Math.random() * 1000);
        return "SO" + timestamp + String.format("%03d", random);
    }
    
    /**
     * 更新订单金额
     * @param order 销售单
     */
    private void updateOrderAmount(SalesOrder order) {
        List<SalesOrderItem> items = salesOrderDao.findOrderItems(order.getId());
        
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (SalesOrderItem item : items) {
            totalAmount = totalAmount.add(item.getSubtotal());
        }
        
        order.setTotalAmount(totalAmount);
        
        // 计算会员折扣
        BigDecimal discountAmount = BigDecimal.ZERO;
        if (order.getMember() != null && order.getMember().getMemberLevel() != null) {
            BigDecimal memberDiscountRate = order.getMember().getMemberLevel().getDiscountRate();
            BigDecimal discountedAmount = totalAmount.multiply(memberDiscountRate);
            discountAmount = totalAmount.subtract(discountedAmount);
            order.setFinalAmount(discountedAmount);
        } else {
            order.setFinalAmount(totalAmount);
        }
        
        order.setDiscountAmount(discountAmount);
        
        // 更新数据库
        salesOrderDao.update(order);
    }
    
    /**
     * 处理会员积分
     * @param order 销售单
     */
    private void processMemberPoints(SalesOrder order) {
        Member member = memberDao.findById(order.getMemberId());
        if (member != null && member.getMemberLevel() != null) {
            // 计算获得的积分
            BigDecimal pointsRate = member.getMemberLevel().getPointsRate();
            int earnedPoints = order.getFinalAmount().multiply(pointsRate).intValue();
            
            // 更新会员积分和消费金额
            memberDao.updatePointsAndConsumption(member.getId(), earnedPoints, order.getFinalAmount());
            
            System.out.println("会员积分已更新，本次获得积分：" + earnedPoints);
        }
    }
    
    /**
     * 打印销售小票
     * @param order 销售单
     */
    private void printReceipt(SalesOrder order) {
        System.out.println("\n" + "=".repeat(40));
        System.out.println("           超市销售小票");
        System.out.println("=".repeat(40));
        System.out.println("销售单号：" + order.getOrderNo());
        System.out.println("收银员：" + (order.getCashier() != null ? order.getCashier().getRealName() : ""));
        System.out.println("时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        if (order.getMember() != null) {
            System.out.println("会员：" + order.getMember().getName() + 
                             " (" + order.getMember().getMemberCode() + ")");
        }
        
        System.out.println("-".repeat(40));
        
        List<SalesOrderItem> items = salesOrderDao.findOrderItems(order.getId());
        for (SalesOrderItem item : items) {
            Product product = productDao.findById(item.getProductId());
            if (product != null) {
                System.out.printf("%-20s %2d x %6.2f = %8.2f%n",
                    product.getProductName(),
                    item.getQuantity(),
                    item.getUnitPrice(),
                    item.getSubtotal());
            }
        }
        
        System.out.println("-".repeat(40));
        System.out.printf("应收金额：%26.2f%n", order.getTotalAmount());
        if (order.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
            System.out.printf("会员优惠：%26.2f%n", order.getDiscountAmount());
        }
        System.out.printf("实收金额：%26.2f%n", order.getFinalAmount());
        System.out.println("=".repeat(40));
        System.out.println("       谢谢惠顾，欢迎再次光临！");
        System.out.println("=".repeat(40) + "\n");
    }
}
