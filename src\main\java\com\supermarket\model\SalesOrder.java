package com.supermarket.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 销售单实体类
 * 对应数据库sales_orders表
 */
public class SalesOrder {
    
    // 订单状态枚举
    public enum Status {
        PENDING("待结账"),
        SUSPENDED("挂单"),
        COMPLETED("已完成"),
        CANCELLED("已撤单");
        
        private final String description;
        
        Status(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    private Integer id;
    private String orderNo;              // 销售单号
    private Integer memberId;            // 会员ID（可为空）
    private Integer cashierId;           // 收银员ID
    private BigDecimal totalAmount;      // 应收总金额
    private BigDecimal discountAmount;   // 折扣金额
    private BigDecimal finalAmount;      // 实收金额
    private Status status;               // 订单状态
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 关联对象
    private Member member;               // 会员信息
    private User cashier;                // 收银员信息
    private List<SalesOrderItem> items;  // 销售单明细
    
    // 构造方法
    public SalesOrder() {
        this.totalAmount = BigDecimal.ZERO;
        this.discountAmount = BigDecimal.ZERO;
        this.finalAmount = BigDecimal.ZERO;
        this.status = Status.PENDING;
    }
    
    public SalesOrder(String orderNo, Integer cashierId) {
        this();
        this.orderNo = orderNo;
        this.cashierId = cashierId;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getOrderNo() {
        return orderNo;
    }
    
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
    
    public Integer getMemberId() {
        return memberId;
    }
    
    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }
    
    public Integer getCashierId() {
        return cashierId;
    }
    
    public void setCashierId(Integer cashierId) {
        this.cashierId = cashierId;
    }
    
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }
    
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
    
    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }
    
    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }
    
    public BigDecimal getFinalAmount() {
        return finalAmount;
    }
    
    public void setFinalAmount(BigDecimal finalAmount) {
        this.finalAmount = finalAmount;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public void setStatus(Status status) {
        this.status = status;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Member getMember() {
        return member;
    }
    
    public void setMember(Member member) {
        this.member = member;
    }
    
    public User getCashier() {
        return cashier;
    }
    
    public void setCashier(User cashier) {
        this.cashier = cashier;
    }
    
    public List<SalesOrderItem> getItems() {
        return items;
    }
    
    public void setItems(List<SalesOrderItem> items) {
        this.items = items;
    }
    
    @Override
    public String toString() {
        return "SalesOrder{" +
                "id=" + id +
                ", orderNo='" + orderNo + '\'' +
                ", memberId=" + memberId +
                ", cashierId=" + cashierId +
                ", totalAmount=" + totalAmount +
                ", discountAmount=" + discountAmount +
                ", finalAmount=" + finalAmount +
                ", status=" + status +
                '}';
    }
}
