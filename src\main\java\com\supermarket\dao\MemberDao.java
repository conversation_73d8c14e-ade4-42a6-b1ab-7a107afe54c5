package com.supermarket.dao;

import com.supermarket.config.DatabaseConfig;
import com.supermarket.model.Member;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 会员数据访问对象
 * 负责会员相关的数据库操作
 */
public class MemberDao {
    
    /**
     * 根据会员卡号查询会员
     * @param memberCode 会员卡号
     * @return 会员对象，如果不存在返回null
     */
    public Member findByMemberCode(String memberCode) {
        String sql = "SELECT m.*, ml.level_name, ml.discount_rate, ml.points_rate " +
                    "FROM members m LEFT JOIN member_levels ml ON m.level_id = ml.id " +
                    "WHERE m.member_code = ? AND m.status = 'ACTIVE'";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, memberCode);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToMember(rs);
                }
            }
        } catch (SQLException e) {
            System.err.println("查询会员失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 根据ID查询会员
     * @param id 会员ID
     * @return 会员对象，如果不存在返回null
     */
    public Member findById(Integer id) {
        String sql = "SELECT m.*, ml.level_name, ml.discount_rate, ml.points_rate " +
                    "FROM members m LEFT JOIN member_levels ml ON m.level_id = ml.id " +
                    "WHERE m.id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, id);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToMember(rs);
                }
            }
        } catch (SQLException e) {
            System.err.println("查询会员失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 查询所有会员
     * @return 会员列表
     */
    public List<Member> findAll() {
        List<Member> members = new ArrayList<>();
        String sql = "SELECT m.*, ml.level_name, ml.discount_rate, ml.points_rate " +
                    "FROM members m LEFT JOIN member_levels ml ON m.level_id = ml.id " +
                    "ORDER BY m.created_at DESC";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                members.add(mapResultSetToMember(rs));
            }
        } catch (SQLException e) {
            System.err.println("查询会员列表失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return members;
    }
    
    /**
     * 添加新会员
     * @param member 会员对象
     * @return 是否添加成功
     */
    public boolean insert(Member member) {
        String sql = "INSERT INTO members (member_code, name, phone, email, level_id, " +
                    "total_points, available_points, total_consumption, status) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, member.getMemberCode());
            stmt.setString(2, member.getName());
            stmt.setString(3, member.getPhone());
            stmt.setString(4, member.getEmail());
            stmt.setInt(5, member.getLevelId());
            stmt.setInt(6, member.getTotalPoints());
            stmt.setInt(7, member.getAvailablePoints());
            stmt.setBigDecimal(8, member.getTotalConsumption());
            stmt.setString(9, member.getStatus().name());
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("添加会员失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 更新会员信息
     * @param member 会员对象
     * @return 是否更新成功
     */
    public boolean update(Member member) {
        String sql = "UPDATE members SET name = ?, phone = ?, email = ?, level_id = ?, " +
                    "total_points = ?, available_points = ?, total_consumption = ?, status = ? " +
                    "WHERE id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, member.getName());
            stmt.setString(2, member.getPhone());
            stmt.setString(3, member.getEmail());
            stmt.setInt(4, member.getLevelId());
            stmt.setInt(5, member.getTotalPoints());
            stmt.setInt(6, member.getAvailablePoints());
            stmt.setBigDecimal(7, member.getTotalConsumption());
            stmt.setString(8, member.getStatus().name());
            stmt.setInt(9, member.getId());
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("更新会员失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 更新会员积分和消费金额
     * @param memberId 会员ID
     * @param pointsChange 积分变化
     * @param consumptionAmount 消费金额
     * @return 是否更新成功
     */
    public boolean updatePointsAndConsumption(Integer memberId, Integer pointsChange, 
                                            java.math.BigDecimal consumptionAmount) {
        String sql = "UPDATE members SET total_points = total_points + ?, " +
                    "available_points = available_points + ?, " +
                    "total_consumption = total_consumption + ? WHERE id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, pointsChange);
            stmt.setInt(2, pointsChange);
            stmt.setBigDecimal(3, consumptionAmount);
            stmt.setInt(4, memberId);
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("更新会员积分和消费金额失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 更新会员等级
     * @param memberId 会员ID
     * @param newLevelId 新等级ID
     * @param pointsDeducted 扣除的积分
     * @return 是否更新成功
     */
    public boolean updateLevel(Integer memberId, Integer newLevelId, Integer pointsDeducted) {
        String sql = "UPDATE members SET level_id = ?, available_points = available_points - ? WHERE id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, newLevelId);
            stmt.setInt(2, pointsDeducted);
            stmt.setInt(3, memberId);
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("更新会员等级失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 查询可升级的会员列表
     * @return 可升级的会员列表
     */
    public List<Member> findUpgradeableMembers() {
        List<Member> members = new ArrayList<>();
        String sql = "SELECT m.*, ml.level_name, ml.discount_rate, ml.points_rate " +
                    "FROM members m " +
                    "LEFT JOIN member_levels ml ON m.level_id = ml.id " +
                    "WHERE m.available_points >= (" +
                    "    SELECT MIN(ml2.min_points) FROM member_levels ml2 " +
                    "    WHERE ml2.id > m.level_id" +
                    ") AND m.status = 'ACTIVE'";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                members.add(mapResultSetToMember(rs));
            }
        } catch (SQLException e) {
            System.err.println("查询可升级会员失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return members;
    }
    
    /**
     * 将ResultSet映射为Member对象
     * @param rs ResultSet对象
     * @return Member对象
     * @throws SQLException SQL异常
     */
    private Member mapResultSetToMember(ResultSet rs) throws SQLException {
        Member member = new Member();
        member.setId(rs.getInt("id"));
        member.setMemberCode(rs.getString("member_code"));
        member.setName(rs.getString("name"));
        member.setPhone(rs.getString("phone"));
        member.setEmail(rs.getString("email"));
        member.setLevelId(rs.getInt("level_id"));
        member.setTotalPoints(rs.getInt("total_points"));
        member.setAvailablePoints(rs.getInt("available_points"));
        member.setTotalConsumption(rs.getBigDecimal("total_consumption"));
        member.setStatus(Member.Status.valueOf(rs.getString("status")));
        member.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
        member.setUpdatedAt(rs.getTimestamp("updated_at").toLocalDateTime());
        return member;
    }
}
