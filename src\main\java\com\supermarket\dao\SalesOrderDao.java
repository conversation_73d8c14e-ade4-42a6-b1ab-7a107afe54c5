package com.supermarket.dao;

import com.supermarket.config.DatabaseConfig;
import com.supermarket.model.SalesOrder;
import com.supermarket.model.SalesOrderItem;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 销售单数据访问对象
 * 负责销售单相关的数据库操作
 */
public class SalesOrderDao {
    
    /**
     * 根据订单号查询销售单
     * @param orderNo 订单号
     * @return 销售单对象，如果不存在返回null
     */
    public SalesOrder findByOrderNo(String orderNo) {
        String sql = "SELECT so.*, m.name as member_name, u.real_name as cashier_name " +
                    "FROM sales_orders so " +
                    "LEFT JOIN members m ON so.member_id = m.id " +
                    "LEFT JOIN users u ON so.cashier_id = u.id " +
                    "WHERE so.order_no = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, orderNo);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToSalesOrder(rs);
                }
            }
        } catch (SQLException e) {
            System.err.println("查询销售单失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 根据ID查询销售单
     * @param id 销售单ID
     * @return 销售单对象，如果不存在返回null
     */
    public SalesOrder findById(Integer id) {
        String sql = "SELECT so.*, m.name as member_name, u.real_name as cashier_name " +
                    "FROM sales_orders so " +
                    "LEFT JOIN members m ON so.member_id = m.id " +
                    "LEFT JOIN users u ON so.cashier_id = u.id " +
                    "WHERE so.id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, id);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToSalesOrder(rs);
                }
            }
        } catch (SQLException e) {
            System.err.println("查询销售单失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 查询挂单列表
     * @return 挂单列表
     */
    public List<SalesOrder> findSuspendedOrders() {
        List<SalesOrder> orders = new ArrayList<>();
        String sql = "SELECT so.*, m.name as member_name, u.real_name as cashier_name " +
                    "FROM sales_orders so " +
                    "LEFT JOIN members m ON so.member_id = m.id " +
                    "LEFT JOIN users u ON so.cashier_id = u.id " +
                    "WHERE so.status = 'SUSPENDED' ORDER BY so.created_at DESC";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                orders.add(mapResultSetToSalesOrder(rs));
            }
        } catch (SQLException e) {
            System.err.println("查询挂单列表失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return orders;
    }
    
    /**
     * 查询收银员的待处理订单
     * @param cashierId 收银员ID
     * @return 待处理订单列表
     */
    public List<SalesOrder> findPendingOrdersByCashier(Integer cashierId) {
        List<SalesOrder> orders = new ArrayList<>();
        String sql = "SELECT so.*, m.name as member_name, u.real_name as cashier_name " +
                    "FROM sales_orders so " +
                    "LEFT JOIN members m ON so.member_id = m.id " +
                    "LEFT JOIN users u ON so.cashier_id = u.id " +
                    "WHERE so.cashier_id = ? AND so.status = 'PENDING' " +
                    "ORDER BY so.created_at DESC";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, cashierId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    orders.add(mapResultSetToSalesOrder(rs));
                }
            }
        } catch (SQLException e) {
            System.err.println("查询待处理订单失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return orders;
    }
    
    /**
     * 添加新销售单
     * @param order 销售单对象
     * @return 新增销售单的ID，失败返回null
     */
    public Integer insert(SalesOrder order) {
        String sql = "INSERT INTO sales_orders (order_no, member_id, cashier_id, total_amount, " +
                    "discount_amount, final_amount, status) VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setString(1, order.getOrderNo());
            if (order.getMemberId() != null) {
                stmt.setInt(2, order.getMemberId());
            } else {
                stmt.setNull(2, Types.INTEGER);
            }
            stmt.setInt(3, order.getCashierId());
            stmt.setBigDecimal(4, order.getTotalAmount());
            stmt.setBigDecimal(5, order.getDiscountAmount());
            stmt.setBigDecimal(6, order.getFinalAmount());
            stmt.setString(7, order.getStatus().name());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows > 0) {
                try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        return generatedKeys.getInt(1);
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("添加销售单失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 更新销售单
     * @param order 销售单对象
     * @return 是否更新成功
     */
    public boolean update(SalesOrder order) {
        String sql = "UPDATE sales_orders SET member_id = ?, total_amount = ?, " +
                    "discount_amount = ?, final_amount = ?, status = ? WHERE id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            if (order.getMemberId() != null) {
                stmt.setInt(1, order.getMemberId());
            } else {
                stmt.setNull(1, Types.INTEGER);
            }
            stmt.setBigDecimal(2, order.getTotalAmount());
            stmt.setBigDecimal(3, order.getDiscountAmount());
            stmt.setBigDecimal(4, order.getFinalAmount());
            stmt.setString(5, order.getStatus().name());
            stmt.setInt(6, order.getId());
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("更新销售单失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 更新销售单状态
     * @param orderId 销售单ID
     * @param status 新状态
     * @return 是否更新成功
     */
    public boolean updateStatus(Integer orderId, SalesOrder.Status status) {
        String sql = "UPDATE sales_orders SET status = ? WHERE id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, status.name());
            stmt.setInt(2, orderId);
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("更新销售单状态失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 查询销售单明细
     * @param orderId 销售单ID
     * @return 销售单明细列表
     */
    public List<SalesOrderItem> findOrderItems(Integer orderId) {
        List<SalesOrderItem> items = new ArrayList<>();
        String sql = "SELECT soi.*, p.product_name, p.product_code " +
                    "FROM sales_order_items soi " +
                    "LEFT JOIN products p ON soi.product_id = p.id " +
                    "WHERE soi.order_id = ? ORDER BY soi.id";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, orderId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    items.add(mapResultSetToSalesOrderItem(rs));
                }
            }
        } catch (SQLException e) {
            System.err.println("查询销售单明细失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return items;
    }
    
    /**
     * 添加销售单明细
     * @param item 销售单明细对象
     * @return 是否添加成功
     */
    public boolean insertOrderItem(SalesOrderItem item) {
        String sql = "INSERT INTO sales_order_items (order_id, product_id, quantity, " +
                    "unit_price, discount_rate, subtotal) VALUES (?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, item.getOrderId());
            stmt.setInt(2, item.getProductId());
            stmt.setInt(3, item.getQuantity());
            stmt.setBigDecimal(4, item.getUnitPrice());
            stmt.setBigDecimal(5, item.getDiscountRate());
            stmt.setBigDecimal(6, item.getSubtotal());
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("添加销售单明细失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 将ResultSet映射为SalesOrder对象
     * @param rs ResultSet对象
     * @return SalesOrder对象
     * @throws SQLException SQL异常
     */
    private SalesOrder mapResultSetToSalesOrder(ResultSet rs) throws SQLException {
        SalesOrder order = new SalesOrder();
        order.setId(rs.getInt("id"));
        order.setOrderNo(rs.getString("order_no"));
        order.setMemberId(rs.getObject("member_id", Integer.class));
        order.setCashierId(rs.getInt("cashier_id"));
        order.setTotalAmount(rs.getBigDecimal("total_amount"));
        order.setDiscountAmount(rs.getBigDecimal("discount_amount"));
        order.setFinalAmount(rs.getBigDecimal("final_amount"));
        order.setStatus(SalesOrder.Status.valueOf(rs.getString("status")));
        order.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
        order.setUpdatedAt(rs.getTimestamp("updated_at").toLocalDateTime());
        return order;
    }
    
    /**
     * 将ResultSet映射为SalesOrderItem对象
     * @param rs ResultSet对象
     * @return SalesOrderItem对象
     * @throws SQLException SQL异常
     */
    private SalesOrderItem mapResultSetToSalesOrderItem(ResultSet rs) throws SQLException {
        SalesOrderItem item = new SalesOrderItem();
        item.setId(rs.getInt("id"));
        item.setOrderId(rs.getInt("order_id"));
        item.setProductId(rs.getInt("product_id"));
        item.setQuantity(rs.getInt("quantity"));
        item.setUnitPrice(rs.getBigDecimal("unit_price"));
        item.setDiscountRate(rs.getBigDecimal("discount_rate"));
        item.setSubtotal(rs.getBigDecimal("subtotal"));
        item.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
        return item;
    }
}
