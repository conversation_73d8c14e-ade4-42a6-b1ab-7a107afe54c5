#!/bin/bash

echo "超市前台销售系统启动脚本"
echo "================================"

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误：未找到Java环境，请确保已安装Java 8或更高版本"
    exit 1
fi

# 检查MySQL JDBC驱动
if [ ! -f "mysql-connector-java-8.0.33.jar" ]; then
    echo "警告：未找到MySQL JDBC驱动文件 mysql-connector-java-8.0.33.jar"
    echo "请下载MySQL JDBC驱动并放置在项目根目录下"
    echo "下载地址：https://dev.mysql.com/downloads/connector/j/"
    read -p "按回车键继续..."
fi

# 创建输出目录
mkdir -p out

echo "正在编译Java源代码..."

# 编译所有Java文件
javac -cp "mysql-connector-java-8.0.33.jar" -d out -encoding UTF-8 \
    src/main/java/com/supermarket/*.java \
    src/main/java/com/supermarket/*/*.java

if [ $? -ne 0 ]; then
    echo "编译失败，请检查代码错误"
    exit 1
fi

echo "编译成功！"
echo "正在启动系统..."
echo

# 运行程序
java -cp "out:mysql-connector-java-8.0.33.jar" com.supermarket.Main

echo
echo "系统已退出"
