package com.supermarket.service;

import com.supermarket.dao.UserDao;
import com.supermarket.model.User;

import java.util.List;

/**
 * 用户服务类
 * 负责用户管理相关的业务逻辑
 */
public class UserService {
    
    private UserDao userDao;
    
    public UserService() {
        this.userDao = new UserDao();
    }
    
    /**
     * 用户登录验证
     * @param username 用户名
     * @param password 密码
     * @return 登录成功返回用户对象，失败返回null
     */
    public User login(String username, String password) {
        if (username == null || username.trim().isEmpty()) {
            System.out.println("用户名不能为空");
            return null;
        }
        
        if (password == null || password.trim().isEmpty()) {
            System.out.println("密码不能为空");
            return null;
        }
        
        User user = userDao.findByUsernameAndPassword(username.trim(), password);
        if (user == null) {
            System.out.println("用户名或密码错误，或账号已被停用");
            return null;
        }
        
        System.out.println("登录成功，欢迎 " + user.getRealName() + " (" + user.getRole().getDescription() + ")");
        return user;
    }
    
    /**
     * 添加新用户（系统管理员功能）
     * @param username 用户名
     * @param role 用户角色
     * @param realName 真实姓名
     * @return 是否添加成功
     */
    public boolean addUser(String username, User.Role role, String realName) {
        // 验证输入参数
        if (username == null || username.trim().isEmpty()) {
            System.out.println("用户名不能为空");
            return false;
        }
        
        if (!username.matches("^[a-zA-Z][a-zA-Z0-9_]*$")) {
            System.out.println("用户名必须是全英文，以字母开头，只能包含字母、数字和下划线");
            return false;
        }
        
        if (role == null) {
            System.out.println("用户角色不能为空");
            return false;
        }
        
        if (realName == null || realName.trim().isEmpty()) {
            System.out.println("真实姓名不能为空");
            return false;
        }
        
        // 检查用户名是否已存在
        User existingUser = userDao.findByUsername(username.trim());
        if (existingUser != null) {
            System.out.println("用户名已存在，请选择其他用户名");
            return false;
        }
        
        // 创建新用户，初始密码为123
        User newUser = new User(username.trim(), "123", role, realName.trim());
        
        boolean success = userDao.insert(newUser);
        if (success) {
            System.out.println("用户添加成功，初始密码为：123");
        } else {
            System.out.println("用户添加失败");
        }
        
        return success;
    }
    
    /**
     * 修改用户密码
     * @param userId 用户ID
     * @param oldPassword 原密码
     * @param newPassword 新密码
     * @return 是否修改成功
     */
    public boolean changePassword(Integer userId, String oldPassword, String newPassword) {
        if (oldPassword == null || oldPassword.trim().isEmpty()) {
            System.out.println("原密码不能为空");
            return false;
        }
        
        if (newPassword == null || newPassword.trim().isEmpty()) {
            System.out.println("新密码不能为空");
            return false;
        }
        
        if (oldPassword.equals(newPassword)) {
            System.out.println("新密码不能与原密码相同");
            return false;
        }
        
        // 验证原密码
        User user = userDao.findById(userId);
        if (user == null) {
            System.out.println("用户不存在");
            return false;
        }
        
        if (!user.getPassword().equals(oldPassword)) {
            System.out.println("原密码错误");
            return false;
        }
        
        // 更新密码
        boolean success = userDao.updatePassword(userId, newPassword);
        if (success) {
            System.out.println("密码修改成功");
        } else {
            System.out.println("密码修改失败");
        }
        
        return success;
    }
    
    /**
     * 重置用户密码（系统管理员功能）
     * @param userId 用户ID
     * @return 是否重置成功
     */
    public boolean resetPassword(Integer userId) {
        User user = userDao.findById(userId);
        if (user == null) {
            System.out.println("用户不存在");
            return false;
        }
        
        boolean success = userDao.updatePassword(userId, "123");
        if (success) {
            System.out.println("密码重置成功，新密码为：123");
        } else {
            System.out.println("密码重置失败");
        }
        
        return success;
    }
    
    /**
     * 启用/停用用户账号（系统管理员功能）
     * @param userId 用户ID
     * @param status 用户状态
     * @return 是否操作成功
     */
    public boolean updateUserStatus(Integer userId, User.Status status) {
        User user = userDao.findById(userId);
        if (user == null) {
            System.out.println("用户不存在");
            return false;
        }
        
        if (user.getStatus() == status) {
            System.out.println("用户状态无需更改");
            return true;
        }
        
        boolean success = userDao.updateStatus(userId, status);
        if (success) {
            System.out.println("用户状态更新成功：" + status.getDescription());
        } else {
            System.out.println("用户状态更新失败");
        }
        
        return success;
    }
    
    /**
     * 查询所有用户（系统管理员功能）
     * @return 用户列表
     */
    public List<User> getAllUsers() {
        return userDao.findAll();
    }
    
    /**
     * 根据ID查询用户
     * @param userId 用户ID
     * @return 用户对象
     */
    public User getUserById(Integer userId) {
        return userDao.findById(userId);
    }
    
    /**
     * 检查用户权限
     * @param user 当前用户
     * @param requiredRole 需要的角色
     * @return 是否有权限
     */
    public boolean hasPermission(User user, User.Role requiredRole) {
        if (user == null) {
            return false;
        }
        
        // 系统管理员拥有所有权限
        if (user.getRole() == User.Role.ADMIN) {
            return true;
        }
        
        // 检查具体角色权限
        return user.getRole() == requiredRole;
    }
    
    /**
     * 检查用户是否有多个角色中的任一权限
     * @param user 当前用户
     * @param roles 角色列表
     * @return 是否有权限
     */
    public boolean hasAnyPermission(User user, User.Role... roles) {
        if (user == null) {
            return false;
        }
        
        // 系统管理员拥有所有权限
        if (user.getRole() == User.Role.ADMIN) {
            return true;
        }
        
        // 检查是否有任一角色权限
        for (User.Role role : roles) {
            if (user.getRole() == role) {
                return true;
            }
        }
        
        return false;
    }
}
