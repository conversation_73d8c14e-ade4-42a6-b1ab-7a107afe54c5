package com.supermarket.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销售单明细实体类
 * 对应数据库sales_order_items表
 */
public class SalesOrderItem {
    
    private Integer id;
    private Integer orderId;         // 销售单ID
    private Integer productId;       // 商品ID
    private Integer quantity;        // 商品数量
    private BigDecimal unitPrice;    // 单价
    private BigDecimal discountRate; // 折扣率
    private BigDecimal subtotal;     // 小计金额
    private LocalDateTime createdAt;
    
    // 关联对象
    private Product product;         // 商品信息
    
    // 构造方法
    public SalesOrderItem() {
        this.discountRate = BigDecimal.ONE; // 默认无折扣
    }
    
    public SalesOrderItem(Integer orderId, Integer productId, Integer quantity, BigDecimal unitPrice) {
        this();
        this.orderId = orderId;
        this.productId = productId;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        // 计算小计金额
        this.subtotal = unitPrice.multiply(BigDecimal.valueOf(quantity)).multiply(discountRate);
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public Integer getOrderId() {
        return orderId;
    }
    
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }
    
    public Integer getProductId() {
        return productId;
    }
    
    public void setProductId(Integer productId) {
        this.productId = productId;
    }
    
    public Integer getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
        // 重新计算小计金额
        if (this.unitPrice != null && this.discountRate != null) {
            this.subtotal = this.unitPrice.multiply(BigDecimal.valueOf(quantity)).multiply(this.discountRate);
        }
    }
    
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }
    
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
        // 重新计算小计金额
        if (this.quantity != null && this.discountRate != null) {
            this.subtotal = unitPrice.multiply(BigDecimal.valueOf(this.quantity)).multiply(this.discountRate);
        }
    }
    
    public BigDecimal getDiscountRate() {
        return discountRate;
    }
    
    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
        // 重新计算小计金额
        if (this.unitPrice != null && this.quantity != null) {
            this.subtotal = this.unitPrice.multiply(BigDecimal.valueOf(this.quantity)).multiply(discountRate);
        }
    }
    
    public BigDecimal getSubtotal() {
        return subtotal;
    }
    
    public void setSubtotal(BigDecimal subtotal) {
        this.subtotal = subtotal;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public Product getProduct() {
        return product;
    }
    
    public void setProduct(Product product) {
        this.product = product;
    }
    
    /**
     * 计算小计金额
     */
    public void calculateSubtotal() {
        if (unitPrice != null && quantity != null && discountRate != null) {
            this.subtotal = unitPrice.multiply(BigDecimal.valueOf(quantity)).multiply(discountRate);
        }
    }
    
    @Override
    public String toString() {
        return "SalesOrderItem{" +
                "id=" + id +
                ", orderId=" + orderId +
                ", productId=" + productId +
                ", quantity=" + quantity +
                ", unitPrice=" + unitPrice +
                ", discountRate=" + discountRate +
                ", subtotal=" + subtotal +
                '}';
    }
}
