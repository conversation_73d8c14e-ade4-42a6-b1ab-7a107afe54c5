package com.supermarket.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员等级实体类
 * 对应数据库member_levels表
 */
public class MemberLevel {
    
    private Integer id;
    private String levelName;        // 等级名称
    private Integer minPoints;       // 升级所需最小积分
    private BigDecimal discountRate; // 折扣率
    private BigDecimal pointsRate;   // 积分比例（每元消费获得积分）
    private LocalDateTime createdAt;
    
    // 构造方法
    public MemberLevel() {}
    
    public MemberLevel(String levelName, Integer minPoints, BigDecimal discountRate, BigDecimal pointsRate) {
        this.levelName = levelName;
        this.minPoints = minPoints;
        this.discountRate = discountRate;
        this.pointsRate = pointsRate;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getLevelName() {
        return levelName;
    }
    
    public void setLevelName(String levelName) {
        this.levelName = levelName;
    }
    
    public Integer getMinPoints() {
        return minPoints;
    }
    
    public void setMinPoints(Integer minPoints) {
        this.minPoints = minPoints;
    }
    
    public BigDecimal getDiscountRate() {
        return discountRate;
    }
    
    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }
    
    public BigDecimal getPointsRate() {
        return pointsRate;
    }
    
    public void setPointsRate(BigDecimal pointsRate) {
        this.pointsRate = pointsRate;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "MemberLevel{" +
                "id=" + id +
                ", levelName='" + levelName + '\'' +
                ", minPoints=" + minPoints +
                ", discountRate=" + discountRate +
                ", pointsRate=" + pointsRate +
                '}';
    }
}
