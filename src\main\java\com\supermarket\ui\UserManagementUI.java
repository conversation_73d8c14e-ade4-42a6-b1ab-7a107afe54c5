package com.supermarket.ui;

import com.supermarket.model.User;
import com.supermarket.service.UserService;

import java.util.List;
import java.util.Scanner;

/**
 * 用户管理界面
 * 负责用户管理相关功能的用户交互（系统管理员功能）
 */
public class UserManagementUI {
    
    private Scanner scanner;
    private User currentUser;
    private UserService userService;
    
    public UserManagementUI(Scanner scanner, User currentUser) {
        this.scanner = scanner;
        this.currentUser = currentUser;
        this.userService = new UserService();
    }
    
    /**
     * 显示用户管理菜单
     */
    public void show() {
        // 检查权限
        if (!userService.hasPermission(currentUser, User.Role.ADMIN)) {
            System.out.println("权限不足，无法访问用户管理功能");
            return;
        }
        
        while (true) {
            System.out.println("\n" + "=".repeat(40));
            System.out.println("           用户管理");
            System.out.println("=".repeat(40));
            System.out.println("1. 查看所有用户");
            System.out.println("2. 添加新用户");
            System.out.println("3. 启用/停用用户");
            System.out.println("4. 重置用户密码");
            System.out.println("0. 返回主菜单");
            System.out.print("请选择功能: ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    showAllUsers();
                    break;
                case "2":
                    addUser();
                    break;
                case "3":
                    toggleUserStatus();
                    break;
                case "4":
                    resetUserPassword();
                    break;
                case "0":
                    return;
                default:
                    System.out.println("无效的选项，请重新选择");
            }
        }
    }
    
    /**
     * 查看所有用户
     */
    private void showAllUsers() {
        List<User> users = userService.getAllUsers();
        
        if (users.isEmpty()) {
            System.out.println("系统中暂无用户");
            return;
        }
        
        System.out.println("\n--- 用户列表 ---");
        System.out.printf("%-5s %-15s %-15s %-10s %-10s%n", 
                         "ID", "用户名", "真实姓名", "角色", "状态");
        System.out.println("-".repeat(70));
        
        for (User user : users) {
            System.out.printf("%-5d %-15s %-15s %-10s %-10s%n",
                user.getId(),
                user.getUsername(),
                user.getRealName(),
                user.getRole().getDescription(),
                user.getStatus().getDescription());
        }
    }
    
    /**
     * 添加新用户
     */
    private void addUser() {
        System.out.println("\n--- 添加新用户 ---");
        
        System.out.print("请输入用户名（全英文）: ");
        String username = scanner.nextLine().trim();
        
        if (username.isEmpty()) {
            System.out.println("用户名不能为空");
            return;
        }
        
        System.out.print("请输入真实姓名: ");
        String realName = scanner.nextLine().trim();
        
        if (realName.isEmpty()) {
            System.out.println("真实姓名不能为空");
            return;
        }
        
        System.out.println("请选择用户角色：");
        System.out.println("1. 系统管理员");
        System.out.println("2. 商品管理员");
        System.out.println("3. 收银员");
        System.out.println("4. 会员");
        System.out.print("请选择角色 (1-4): ");
        
        String roleChoice = scanner.nextLine().trim();
        User.Role role;
        
        switch (roleChoice) {
            case "1":
                role = User.Role.ADMIN;
                break;
            case "2":
                role = User.Role.PRODUCT_MANAGER;
                break;
            case "3":
                role = User.Role.CASHIER;
                break;
            case "4":
                role = User.Role.MEMBER;
                break;
            default:
                System.out.println("无效的角色选择");
                return;
        }
        
        System.out.println("\n用户信息确认：");
        System.out.println("用户名：" + username);
        System.out.println("真实姓名：" + realName);
        System.out.println("角色：" + role.getDescription());
        System.out.println("初始密码：123");
        
        System.out.print("确认添加用户？(y/n): ");
        String confirm = scanner.nextLine().trim().toLowerCase();
        
        if (confirm.equals("y") || confirm.equals("yes")) {
            boolean success = userService.addUser(username, role, realName);
            if (success) {
                System.out.println("用户添加成功");
            }
        } else {
            System.out.println("已取消添加用户");
        }
    }
    
    /**
     * 启用/停用用户
     */
    private void toggleUserStatus() {
        System.out.println("\n--- 启用/停用用户 ---");
        
        // 先显示用户列表
        showAllUsers();
        
        System.out.print("请输入要操作的用户ID: ");
        String userIdStr = scanner.nextLine().trim();
        
        if (userIdStr.isEmpty()) {
            System.out.println("用户ID不能为空");
            return;
        }
        
        try {
            Integer userId = Integer.parseInt(userIdStr);
            
            // 不能操作自己的账号
            if (userId.equals(currentUser.getId())) {
                System.out.println("不能操作自己的账号");
                return;
            }
            
            User user = userService.getUserById(userId);
            if (user == null) {
                System.out.println("用户不存在");
                return;
            }
            
            System.out.println("当前用户：" + user.getUsername() + " (" + user.getRealName() + ")");
            System.out.println("当前状态：" + user.getStatus().getDescription());
            
            User.Status newStatus = (user.getStatus() == User.Status.ACTIVE) ? 
                                   User.Status.INACTIVE : User.Status.ACTIVE;
            
            System.out.print("确认将用户状态改为 " + newStatus.getDescription() + "？(y/n): ");
            String confirm = scanner.nextLine().trim().toLowerCase();
            
            if (confirm.equals("y") || confirm.equals("yes")) {
                boolean success = userService.updateUserStatus(userId, newStatus);
                if (success) {
                    System.out.println("用户状态更新成功");
                }
            } else {
                System.out.println("已取消操作");
            }
            
        } catch (NumberFormatException e) {
            System.out.println("请输入有效的用户ID");
        }
    }
    
    /**
     * 重置用户密码
     */
    private void resetUserPassword() {
        System.out.println("\n--- 重置用户密码 ---");
        
        // 先显示用户列表
        showAllUsers();
        
        System.out.print("请输入要重置密码的用户ID: ");
        String userIdStr = scanner.nextLine().trim();
        
        if (userIdStr.isEmpty()) {
            System.out.println("用户ID不能为空");
            return;
        }
        
        try {
            Integer userId = Integer.parseInt(userIdStr);
            
            User user = userService.getUserById(userId);
            if (user == null) {
                System.out.println("用户不存在");
                return;
            }
            
            System.out.println("要重置密码的用户：" + user.getUsername() + " (" + user.getRealName() + ")");
            System.out.print("确认重置密码为123？(y/n): ");
            String confirm = scanner.nextLine().trim().toLowerCase();
            
            if (confirm.equals("y") || confirm.equals("yes")) {
                boolean success = userService.resetPassword(userId);
                if (success) {
                    System.out.println("密码重置成功");
                }
            } else {
                System.out.println("已取消重置密码");
            }
            
        } catch (NumberFormatException e) {
            System.out.println("请输入有效的用户ID");
        }
    }
}
