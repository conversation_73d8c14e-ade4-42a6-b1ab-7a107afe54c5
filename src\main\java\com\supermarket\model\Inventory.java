package com.supermarket.model;

import java.time.LocalDateTime;

/**
 * 库存实体类
 * 对应数据库inventory表
 */
public class Inventory {
    
    private Integer id;
    private Integer shelfId;         // 货架ID
    private Integer productId;       // 商品ID
    private Integer quantity;        // 当前库存数量
    private Integer minStock;        // 最小库存警戒值
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 关联对象
    private Shelf shelf;             // 货架信息
    private Product product;         // 商品信息
    
    // 构造方法
    public Inventory() {}
    
    public Inventory(Integer shelfId, Integer productId, Integer quantity, Integer minStock) {
        this.shelfId = shelfId;
        this.productId = productId;
        this.quantity = quantity;
        this.minStock = minStock;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public Integer getShelfId() {
        return shelfId;
    }
    
    public void setShelfId(Integer shelfId) {
        this.shelfId = shelfId;
    }
    
    public Integer getProductId() {
        return productId;
    }
    
    public void setProductId(Integer productId) {
        this.productId = productId;
    }
    
    public Integer getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
    
    public Integer getMinStock() {
        return minStock;
    }
    
    public void setMinStock(Integer minStock) {
        this.minStock = minStock;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Shelf getShelf() {
        return shelf;
    }
    
    public void setShelf(Shelf shelf) {
        this.shelf = shelf;
    }
    
    public Product getProduct() {
        return product;
    }
    
    public void setProduct(Product product) {
        this.product = product;
    }
    
    /**
     * 检查是否需要补货
     * @return 是否需要补货
     */
    public boolean needRestock() {
        return quantity <= minStock;
    }
    
    @Override
    public String toString() {
        return "Inventory{" +
                "id=" + id +
                ", shelfId=" + shelfId +
                ", productId=" + productId +
                ", quantity=" + quantity +
                ", minStock=" + minStock +
                '}';
    }
}
