<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9faf2dc1-d115-47b5-a3ef-aeb1ce947d3d" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\Env\apache-maven-3.9.9" />
        <option name="localRepository" value="E:\Env\apache-maven-3.9.9\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\Env\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 7
}]]></component>
  <component name="ProjectId" id="2yg8kDPz1XgkYJy0kmVSLozPDq5" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.Main.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "F:/Win/BR/PartTime/2.Processing/6.19 控制台前台销售系统 CW 168",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "reference.settings.project.maven.importing",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="F:\Win\BR\PartTime\2.Processing\6.19 控制台前台销售系统 CW 168" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="Main" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.supermarket.Main" />
      <module name="6.19 控制台前台销售系统 CW 168" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.supermarket.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.Main" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="9faf2dc1-d115-47b5-a3ef-aeb1ce947d3d" name="Changes" comment="" />
      <created>1746638827820</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746638827820</updated>
      <workItem from="1750245000162" duration="556000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>