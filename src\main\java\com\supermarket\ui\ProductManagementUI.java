package com.supermarket.ui;

import com.supermarket.model.User;
import com.supermarket.service.UserService;

import java.util.Scanner;

/**
 * 商品管理界面
 * 负责商品管理相关功能的用户交互（商品管理员和系统管理员功能）
 */
public class ProductManagementUI {
    
    private Scanner scanner;
    private User currentUser;
    private UserService userService;
    
    public ProductManagementUI(Scanner scanner, User currentUser) {
        this.scanner = scanner;
        this.currentUser = currentUser;
        this.userService = new UserService();
    }
    
    /**
     * 显示商品管理菜单
     */
    public void show() {
        // 检查权限
        if (!userService.hasAnyPermission(currentUser, User.Role.ADMIN, User.Role.PRODUCT_MANAGER)) {
            System.out.println("权限不足，无法访问商品管理功能");
            return;
        }
        
        while (true) {
            System.out.println("\n" + "=".repeat(40));
            System.out.println("           商品管理");
            System.out.println("=".repeat(40));
            System.out.println("1. 商品分类管理");
            System.out.println("2. 商品信息管理");
            System.out.println("3. 商品上架管理");
            System.out.println("4. 商品定价管理");
            System.out.println("5. 库存管理");
            System.out.println("6. 补货提醒");
            System.out.println("0. 返回主菜单");
            System.out.print("请选择功能: ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    categoryManagement();
                    break;
                case "2":
                    productInfoManagement();
                    break;
                case "3":
                    shelfManagement();
                    break;
                case "4":
                    pricingManagement();
                    break;
                case "5":
                    inventoryManagement();
                    break;
                case "6":
                    restockAlert();
                    break;
                case "0":
                    return;
                default:
                    System.out.println("无效的选项，请重新选择");
            }
        }
    }
    
    /**
     * 商品分类管理
     */
    private void categoryManagement() {
        System.out.println("\n--- 商品分类管理 ---");
        System.out.println("功能说明：按照线分类法对商品进行分类管理");
        System.out.println("此功能正在开发中，敬请期待...");
        
        System.out.println("\n当前系统中的商品分类：");
        System.out.println("01 - 食品饮料");
        System.out.println("  0101 - 休闲食品");
        System.out.println("  0102 - 饮料");
        System.out.println("02 - 日用百货");
        System.out.println("  0201 - 洗护用品");
        System.out.println("  0202 - 厨房用品");
        System.out.println("03 - 服装鞋帽");
        
        System.out.print("\n按回车键返回...");
        scanner.nextLine();
    }
    
    /**
     * 商品信息管理
     */
    private void productInfoManagement() {
        System.out.println("\n--- 商品信息管理 ---");
        System.out.println("功能说明：管理商品基本信息，包括添加、修改、删除商品");
        System.out.println("此功能正在开发中，敬请期待...");
        
        System.out.println("\n当前系统中的示例商品：");
        System.out.println("6901234567890 - 可口可乐 330ml - ¥3.50");
        System.out.println("6901234567891 - 薯片 大包装 - ¥8.90");
        System.out.println("6901234567892 - 洗发水 500ml - ¥25.80");
        System.out.println("6901234567893 - 牙刷 - ¥12.50");
        
        System.out.print("\n按回车键返回...");
        scanner.nextLine();
    }
    
    /**
     * 商品上架管理
     */
    private void shelfManagement() {
        System.out.println("\n--- 商品上架管理 ---");
        System.out.println("功能说明：管理超市分区和货架，对商品进行上架、移架、下架操作");
        System.out.println("货架编码格式：区-架-层（如：A-01-1）");
        System.out.println("此功能正在开发中，敬请期待...");
        
        System.out.println("\n当前系统中的货架：");
        System.out.println("A-01-1 - A区1号货架第1层");
        System.out.println("A-01-2 - A区1号货架第2层");
        System.out.println("A-02-1 - A区2号货架第1层");
        System.out.println("B-01-1 - B区1号货架第1层");
        
        System.out.print("\n按回车键返回...");
        scanner.nextLine();
    }
    
    /**
     * 商品定价管理
     */
    private void pricingManagement() {
        System.out.println("\n--- 商品定价管理 ---");
        System.out.println("功能说明：管理商品价格和折扣规则");
        System.out.println("确保货架上商品价格标牌与系统中的价格绝对一致");
        System.out.println("此功能正在开发中，敬请期待...");
        
        System.out.println("\n定价管理功能包括：");
        System.out.println("1. 商品价格调整");
        System.out.println("2. 折扣规则设定");
        System.out.println("3. 价格标牌同步");
        System.out.println("4. 价格变更历史");
        
        System.out.print("\n按回车键返回...");
        scanner.nextLine();
    }
    
    /**
     * 库存管理
     */
    private void inventoryManagement() {
        System.out.println("\n--- 库存管理 ---");
        System.out.println("功能说明：管理商品库存，记录收入和支出");
        System.out.println("此功能正在开发中，敬请期待...");
        
        System.out.println("\n库存管理功能包括：");
        System.out.println("1. 查看库存状态");
        System.out.println("2. 库存调整");
        System.out.println("3. 库存变动记录");
        System.out.println("4. 库存盘点");
        
        System.out.print("\n按回车键返回...");
        scanner.nextLine();
    }
    
    /**
     * 补货提醒
     */
    private void restockAlert() {
        System.out.println("\n--- 补货提醒 ---");
        System.out.println("功能说明：根据预设的库存警戒值，提醒及时补货");
        System.out.println("此功能正在开发中，敬请期待...");
        
        System.out.println("\n补货提醒功能包括：");
        System.out.println("1. 查看需要补货的商品");
        System.out.println("2. 设置库存警戒值");
        System.out.println("3. 补货提醒历史");
        System.out.println("4. 自动补货建议");
        
        System.out.print("\n按回车键返回...");
        scanner.nextLine();
    }
}
