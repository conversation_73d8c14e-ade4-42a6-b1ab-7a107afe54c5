package com.supermarket.ui;

import com.supermarket.model.User;
import com.supermarket.service.UserService;

import java.util.Scanner;

/**
 * 会员管理界面
 * 负责会员管理相关功能的用户交互（系统管理员功能）
 */
public class MemberManagementUI {
    
    private Scanner scanner;
    private User currentUser;
    private UserService userService;
    
    public MemberManagementUI(Scanner scanner, User currentUser) {
        this.scanner = scanner;
        this.currentUser = currentUser;
        this.userService = new UserService();
    }
    
    /**
     * 显示会员管理菜单
     */
    public void show() {
        // 检查权限
        if (!userService.hasPermission(currentUser, User.Role.ADMIN)) {
            System.out.println("权限不足，无法访问会员管理功能");
            return;
        }
        
        while (true) {
            System.out.println("\n" + "=".repeat(40));
            System.out.println("           会员管理");
            System.out.println("=".repeat(40));
            System.out.println("1. 会员等级规则维护");
            System.out.println("2. 注册新会员");
            System.out.println("3. 会员信息管理");
            System.out.println("4. 会员积分管理");
            System.out.println("5. 会员升级处理");
            System.out.println("6. 查看会员列表");
            System.out.println("0. 返回主菜单");
            System.out.print("请选择功能: ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    memberLevelManagement();
                    break;
                case "2":
                    registerNewMember();
                    break;
                case "3":
                    memberInfoManagement();
                    break;
                case "4":
                    memberPointsManagement();
                    break;
                case "5":
                    memberUpgradeManagement();
                    break;
                case "6":
                    showMemberList();
                    break;
                case "0":
                    return;
                default:
                    System.out.println("无效的选项，请重新选择");
            }
        }
    }
    
    /**
     * 会员等级规则维护
     */
    private void memberLevelManagement() {
        System.out.println("\n--- 会员等级规则维护 ---");
        System.out.println("功能说明：维护会员级别、升级规则、折扣规则等");
        System.out.println("此功能正在开发中，敬请期待...");
        
        System.out.println("\n当前会员等级设置：");
        System.out.println("┌─────────┬─────────┬─────────┬─────────┐");
        System.out.println("│ 等级    │ 升级积分│ 折扣率  │ 积分率  │");
        System.out.println("├─────────┼─────────┼─────────┼─────────┤");
        System.out.println("│ 普通会员│    0    │  1.00   │  0.01   │");
        System.out.println("│ 银卡会员│  1000   │  0.95   │  0.015  │");
        System.out.println("│ 金卡会员│  5000   │  0.90   │  0.02   │");
        System.out.println("│ 钻石会员│ 10000   │  0.85   │  0.025  │");
        System.out.println("└─────────┴─────────┴─────────┴─────────┘");
        
        System.out.print("\n按回车键返回...");
        scanner.nextLine();
    }
    
    /**
     * 注册新会员
     */
    private void registerNewMember() {
        System.out.println("\n--- 注册新会员 ---");
        System.out.println("功能说明：录入会员基本信息，发放会员卡号");
        System.out.println("此功能正在开发中，敬请期待...");
        
        System.out.println("\n会员注册流程：");
        System.out.println("1. 录入会员基本信息（姓名、手机、邮箱等）");
        System.out.println("2. 系统自动生成会员卡号");
        System.out.println("3. 设置初始会员等级（默认普通会员）");
        System.out.println("4. 发放会员卡");
        
        System.out.print("\n按回车键返回...");
        scanner.nextLine();
    }
    
    /**
     * 会员信息管理
     */
    private void memberInfoManagement() {
        System.out.println("\n--- 会员信息管理 ---");
        System.out.println("功能说明：维护会员基本信息，包括修改、停用等操作");
        System.out.println("此功能正在开发中，敬请期待...");
        
        System.out.println("\n会员信息管理功能：");
        System.out.println("1. 查看会员详细信息");
        System.out.println("2. 修改会员基本信息");
        System.out.println("3. 启用/停用会员账号");
        System.out.println("4. 会员卡挂失与补办");
        
        System.out.print("\n按回车键返回...");
        scanner.nextLine();
    }
    
    /**
     * 会员积分管理
     */
    private void memberPointsManagement() {
        System.out.println("\n--- 会员积分管理 ---");
        System.out.println("功能说明：管理会员积分，包括积分记录、积分调整等");
        System.out.println("此功能正在开发中，敬请期待...");
        
        System.out.println("\n积分管理功能：");
        System.out.println("1. 查看会员积分明细");
        System.out.println("2. 手动调整会员积分");
        System.out.println("3. 积分兑换记录");
        System.out.println("4. 积分过期处理");
        
        System.out.println("\n积分获得规则：");
        System.out.println("- 每次购买商品结账后，系统自动记录消费金额");
        System.out.println("- 按照会员等级的积分比例计算获得积分");
        System.out.println("- 积分实时更新到会员账户");
        
        System.out.print("\n按回车键返回...");
        scanner.nextLine();
    }
    
    /**
     * 会员升级处理
     */
    private void memberUpgradeManagement() {
        System.out.println("\n--- 会员升级处理 ---");
        System.out.println("功能说明：处理达到升级条件的会员升级申请");
        System.out.println("此功能正在开发中，敬请期待...");
        
        System.out.println("\n升级处理流程：");
        System.out.println("1. 系统自动检测达到升级条件的会员");
        System.out.println("2. 提请管理员确认会员升级");
        System.out.println("3. 确认后完成会员升级");
        System.out.println("4. 扣除相应升级积分");
        System.out.println("5. 通知会员升级成功");
        
        System.out.println("\n升级条件：");
        System.out.println("- 普通会员 → 银卡会员：1000积分");
        System.out.println("- 银卡会员 → 金卡会员：5000积分");
        System.out.println("- 金卡会员 → 钻石会员：10000积分");
        
        System.out.print("\n按回车键返回...");
        scanner.nextLine();
    }
    
    /**
     * 查看会员列表
     */
    private void showMemberList() {
        System.out.println("\n--- 会员列表 ---");
        System.out.println("功能说明：查看所有注册会员的基本信息");
        System.out.println("此功能正在开发中，敬请期待...");
        
        System.out.println("\n会员列表将显示以下信息：");
        System.out.println("- 会员卡号");
        System.out.println("- 会员姓名");
        System.out.println("- 手机号码");
        System.out.println("- 会员等级");
        System.out.println("- 总积分");
        System.out.println("- 可用积分");
        System.out.println("- 累计消费");
        System.out.println("- 注册时间");
        System.out.println("- 账号状态");
        
        System.out.print("\n按回车键返回...");
        scanner.nextLine();
    }
}
