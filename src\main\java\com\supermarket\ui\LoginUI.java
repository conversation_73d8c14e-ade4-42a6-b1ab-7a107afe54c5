package com.supermarket.ui;

import com.supermarket.model.User;
import com.supermarket.service.UserService;

import java.util.Scanner;

/**
 * 登录界面
 * 负责用户登录和主菜单导航
 */
public class LoginUI {
    
    private Scanner scanner;
    private UserService userService;
    
    public LoginUI() {
        this.scanner = new Scanner(System.in);
        this.userService = new UserService();
    }
    
    /**
     * 显示登录界面
     */
    public void showLogin() {
        System.out.println("\n" + "=".repeat(50));
        System.out.println("           超市前台销售系统");
        System.out.println("=".repeat(50));
        
        while (true) {
            System.out.println("\n请输入登录信息：");
            System.out.print("用户名: ");
            String username = scanner.nextLine().trim();
            
            if (username.isEmpty()) {
                System.out.println("用户名不能为空，请重新输入");
                continue;
            }
            
            System.out.print("密码: ");
            String password = scanner.nextLine().trim();
            
            if (password.isEmpty()) {
                System.out.println("密码不能为空，请重新输入");
                continue;
            }
            
            // 尝试登录
            User user = userService.login(username, password);
            if (user != null) {
                // 登录成功，根据用户角色显示对应菜单
                showMainMenu(user);
                break;
            } else {
                System.out.println("登录失败，请检查用户名和密码");
                System.out.print("是否继续尝试登录？(y/n): ");
                String choice = scanner.nextLine().trim().toLowerCase();
                if (!choice.equals("y") && !choice.equals("yes")) {
                    System.out.println("感谢使用超市前台销售系统！");
                    break;
                }
            }
        }
    }
    
    /**
     * 显示主菜单
     * @param user 当前登录用户
     */
    private void showMainMenu(User user) {
        while (true) {
            System.out.println("\n" + "=".repeat(50));
            System.out.println("当前用户：" + user.getRealName() + " (" + user.getRole().getDescription() + ")");
            System.out.println("=".repeat(50));
            
            // 根据用户角色显示不同的菜单选项
            switch (user.getRole()) {
                case ADMIN:
                    showAdminMenu();
                    break;
                case PRODUCT_MANAGER:
                    showProductManagerMenu();
                    break;
                case CASHIER:
                    showCashierMenu();
                    break;
                case MEMBER:
                    showMemberMenu();
                    break;
            }
            
            System.out.println("0. 修改密码");
            System.out.println("99. 退出登录");
            System.out.print("请选择功能 (输入数字): ");
            
            String choice = scanner.nextLine().trim();
            
            if (choice.equals("99")) {
                System.out.println("已退出登录");
                break;
            } else if (choice.equals("0")) {
                changePassword(user);
            } else {
                handleMenuChoice(user, choice);
            }
        }
    }
    
    /**
     * 显示系统管理员菜单
     */
    private void showAdminMenu() {
        System.out.println("1. 用户管理");
        System.out.println("2. 会员管理");
        System.out.println("3. 商品管理");
        System.out.println("4. 收银功能");
        System.out.println("5. 数据查询");
        System.out.println("6. 销售统计");
    }
    
    /**
     * 显示商品管理员菜单
     */
    private void showProductManagerMenu() {
        System.out.println("1. 商品管理");
        System.out.println("2. 数据查询");
        System.out.println("3. 销售统计");
    }
    
    /**
     * 显示收银员菜单
     */
    private void showCashierMenu() {
        System.out.println("1. 收银功能");
    }
    
    /**
     * 显示会员菜单
     */
    private void showMemberMenu() {
        System.out.println("1. 会员信息查询");
    }
    
    /**
     * 处理菜单选择
     * @param user 当前用户
     * @param choice 用户选择
     */
    private void handleMenuChoice(User user, String choice) {
        try {
            int menuChoice = Integer.parseInt(choice);
            
            switch (user.getRole()) {
                case ADMIN:
                    handleAdminChoice(user, menuChoice);
                    break;
                case PRODUCT_MANAGER:
                    handleProductManagerChoice(user, menuChoice);
                    break;
                case CASHIER:
                    handleCashierChoice(user, menuChoice);
                    break;
                case MEMBER:
                    handleMemberChoice(user, menuChoice);
                    break;
            }
        } catch (NumberFormatException e) {
            System.out.println("请输入有效的数字选项");
        }
    }
    
    /**
     * 处理系统管理员选择
     */
    private void handleAdminChoice(User user, int choice) {
        switch (choice) {
            case 1:
                new UserManagementUI(scanner, user).show();
                break;
            case 2:
                new MemberManagementUI(scanner, user).show();
                break;
            case 3:
                new ProductManagementUI(scanner, user).show();
                break;
            case 4:
                new CashierUI(scanner, user).show();
                break;
            case 5:
                System.out.println("数据查询功能（待实现）");
                break;
            case 6:
                System.out.println("销售统计功能（待实现）");
                break;
            default:
                System.out.println("无效的选项，请重新选择");
        }
    }
    
    /**
     * 处理商品管理员选择
     */
    private void handleProductManagerChoice(User user, int choice) {
        switch (choice) {
            case 1:
                new ProductManagementUI(scanner, user).show();
                break;
            case 2:
                System.out.println("数据查询功能（待实现）");
                break;
            case 3:
                System.out.println("销售统计功能（待实现）");
                break;
            default:
                System.out.println("无效的选项，请重新选择");
        }
    }
    
    /**
     * 处理收银员选择
     */
    private void handleCashierChoice(User user, int choice) {
        switch (choice) {
            case 1:
                new CashierUI(scanner, user).show();
                break;
            default:
                System.out.println("无效的选项，请重新选择");
        }
    }
    
    /**
     * 处理会员选择
     */
    private void handleMemberChoice(User user, int choice) {
        switch (choice) {
            case 1:
                System.out.println("会员信息查询功能（待实现）");
                break;
            default:
                System.out.println("无效的选项，请重新选择");
        }
    }
    
    /**
     * 修改密码功能
     * @param user 当前用户
     */
    private void changePassword(User user) {
        System.out.println("\n--- 修改密码 ---");
        System.out.print("请输入原密码: ");
        String oldPassword = scanner.nextLine().trim();
        
        System.out.print("请输入新密码: ");
        String newPassword = scanner.nextLine().trim();
        
        System.out.print("请确认新密码: ");
        String confirmPassword = scanner.nextLine().trim();
        
        if (!newPassword.equals(confirmPassword)) {
            System.out.println("两次输入的新密码不一致");
            return;
        }
        
        boolean success = userService.changePassword(user.getId(), oldPassword, newPassword);
        if (success) {
            System.out.println("密码修改成功");
        }
    }
}
