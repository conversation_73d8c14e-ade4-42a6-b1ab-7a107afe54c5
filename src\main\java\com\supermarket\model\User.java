package com.supermarket.model;

import java.time.LocalDateTime;

/**
 * 用户实体类
 * 对应数据库users表
 */
public class User {
    
    // 用户角色枚举
    public enum Role {
        ADMIN("系统管理员"),
        PRODUCT_MANAGER("商品管理员"), 
        CASHIER("收银员"),
        MEMBER("会员");
        
        private final String description;
        
        Role(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // 用户状态枚举
    public enum Status {
        ACTIVE("启用"),
        INACTIVE("停用");
        
        private final String description;
        
        Status(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    private Integer id;
    private String username;
    private String password;
    private Role role;
    private String realName;
    private Status status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 构造方法
    public User() {}
    
    public User(String username, String password, Role role, String realName) {
        this.username = username;
        this.password = password;
        this.role = role;
        this.realName = realName;
        this.status = Status.ACTIVE;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public Role getRole() {
        return role;
    }
    
    public void setRole(Role role) {
        this.role = role;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public void setStatus(Status status) {
        this.status = status;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", role=" + role +
                ", realName='" + realName + '\'' +
                ", status=" + status +
                '}';
    }
}
