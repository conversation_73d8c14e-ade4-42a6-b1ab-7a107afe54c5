# 超市前台销售系统

基于Java 8 + MySQL 8 + 控制台界面的超市前台销售系统演示版本。

## 系统概述

本系统是一个简化的超市前台销售系统，主要用于演示超市日常运营中的核心业务流程，包括收银、商品管理、会员管理和用户管理等功能。

## 技术栈

- **后端语言**: Java 8
- **数据库**: MySQL 8.0
- **用户界面**: 控制台界面（命令行交互）
- **架构模式**: 分层架构（UI层、Service层、DAO层、Model层）

## 功能特性

### 1. 收银功能（收银员角色）
- ✅ 开启收银业务，生成唯一销售单号
- ✅ 扫描商品条码，确定商品价格和应收款
- ✅ 支持会员消费积分及优惠折扣
- ✅ 多种结账方式（现金、银行卡、赠券等）
- ✅ 挂单、调单、撤单操作
- ✅ 打印销售小票

### 2. 商品管理功能（商品管理员角色）
- 🚧 商品分类管理（线分类法）
- 🚧 商品上架、移架、下架操作
- 🚧 商品定价和折扣设置
- 🚧 库存管理和补货提醒

### 3. 会员管理功能（系统管理员角色）
- 🚧 会员等级和规则维护
- 🚧 新会员注册和信息维护
- 🚧 会员积分管理
- 🚧 会员升级处理

### 4. 用户管理功能（系统管理员角色）
- ✅ 用户账号添加、停用/启用
- ✅ 密码重置和修改
- ✅ 角色权限管理

> 说明：✅ 表示已完成，🚧 表示基础框架已搭建，功能正在开发中

## 系统角色与权限

| 角色 | 功能权限 |
|------|----------|
| 系统管理员 | 所有功能 |
| 商品管理员 | 商品管理、数据查询、销售统计 |
| 收银员 | 收银功能 |
| 会员 | 会员信息查询 |

## 项目结构

```
src/main/java/com/supermarket/
├── Main.java                    # 主程序入口
├── config/
│   └── DatabaseConfig.java     # 数据库配置
├── model/                       # 实体类
│   ├── User.java               # 用户实体
│   ├── Product.java            # 商品实体
│   ├── Member.java             # 会员实体
│   ├── SalesOrder.java         # 销售单实体
│   └── ...
├── dao/                         # 数据访问层
│   ├── UserDao.java            # 用户数据访问
│   ├── ProductDao.java         # 商品数据访问
│   └── ...
├── service/                     # 业务逻辑层
│   ├── UserService.java        # 用户服务
│   ├── CashierService.java     # 收银服务
│   └── ...
└── ui/                          # 控制台界面
    ├── LoginUI.java            # 登录界面
    ├── CashierUI.java          # 收银界面
    └── ...
```

## 安装与运行

### 环境要求

- Java 8 或更高版本
- MySQL 8.0 或更高版本
- MySQL JDBC驱动（mysql-connector-java）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <项目地址>
   cd supermarket-pos-system
   ```

2. **创建数据库**
   ```bash
   mysql -u root -p < database.sql
   ```

3. **配置数据库连接**
   
   编辑 `src/main/java/com/supermarket/config/DatabaseConfig.java` 文件，修改数据库连接参数：
   ```java
   private static final String URL = "**************************************************************************************************";
   private static final String USERNAME = "root";
   private static final String PASSWORD = "your_password"; // 修改为你的MySQL密码
   ```

4. **添加MySQL JDBC驱动**
   
   下载 `mysql-connector-java-8.0.x.jar` 并添加到项目的classpath中。

5. **编译运行**
   ```bash
   # 编译
   javac -cp ".:mysql-connector-java-8.0.x.jar" src/main/java/com/supermarket/*.java src/main/java/com/supermarket/*/*.java
   
   # 运行
   java -cp ".:mysql-connector-java-8.0.x.jar:src/main/java" com.supermarket.Main
   ```

### 默认账号

系统预置了以下测试账号：

| 用户名 | 密码 | 角色 |
|--------|------|------|
| admin | 123 | 系统管理员 |
| manager | 123 | 商品管理员 |
| cashier | 123 | 收银员 |

## 使用说明

### 收银操作流程

1. 使用收银员账号登录系统
2. 选择"收银功能" → "开启收银业务"
3. 扫描商品条码添加商品到购物车
4. 可选择设置会员信息享受折扣
5. 选择支付方式完成结账
6. 系统自动打印销售小票

### 示例商品条码

系统预置了以下测试商品：

- `6901234567890` - 可口可乐 330ml - ¥3.50
- `6901234567891` - 薯片 大包装 - ¥8.90
- `6901234567892` - 洗发水 500ml - ¥25.80
- `6901234567893` - 牙刷 - ¥12.50

## 数据库设计

系统包含以下主要数据表：

- `users` - 用户表
- `products` - 商品表
- `categories` - 商品分类表
- `members` - 会员表
- `member_levels` - 会员等级表
- `sales_orders` - 销售单表
- `sales_order_items` - 销售单明细表
- `inventory` - 库存表
- `shelves` - 货架表

详细的数据库结构请参考 `database.sql` 文件。

## 注意事项

1. **首次使用**：请确保已执行 `database.sql` 创建数据库和表
2. **用户名规则**：用户名必须是全英文，不能重复
3. **初始密码**：所有用户初始密码为123，建议首次登录后修改
4. **撤单操作**：撤单后订单永久保存但无法恢复，请谨慎操作
5. **权限控制**：系统严格按照角色权限控制功能访问

## 开发说明

本系统采用最简化实现方式，仅作为功能演示。在实际生产环境中使用时，建议进行以下改进：

1. 添加数据验证和异常处理
2. 实现连接池管理数据库连接
3. 添加日志记录功能
4. 实现更完善的权限控制
5. 添加数据备份和恢复功能
6. 优化用户界面体验

## 许可证

本项目仅用于学习和演示目的。
