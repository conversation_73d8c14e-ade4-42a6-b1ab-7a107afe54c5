package com.supermarket.dao;

import com.supermarket.config.DatabaseConfig;
import com.supermarket.model.Product;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品数据访问对象
 * 负责商品相关的数据库操作
 */
public class ProductDao {
    
    /**
     * 根据商品编码查询商品
     * @param productCode 商品编码/条码
     * @return 商品对象，如果不存在返回null
     */
    public Product findByProductCode(String productCode) {
        String sql = "SELECT p.*, c.category_name FROM products p " +
                    "LEFT JOIN categories c ON p.category_id = c.id " +
                    "WHERE p.product_code = ? AND p.status = 'ON_SHELF'";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, productCode);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToProduct(rs);
                }
            }
        } catch (SQLException e) {
            System.err.println("查询商品失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 根据ID查询商品
     * @param id 商品ID
     * @return 商品对象，如果不存在返回null
     */
    public Product findById(Integer id) {
        String sql = "SELECT p.*, c.category_name FROM products p " +
                    "LEFT JOIN categories c ON p.category_id = c.id " +
                    "WHERE p.id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, id);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToProduct(rs);
                }
            }
        } catch (SQLException e) {
            System.err.println("查询商品失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 查询所有商品
     * @return 商品列表
     */
    public List<Product> findAll() {
        List<Product> products = new ArrayList<>();
        String sql = "SELECT p.*, c.category_name FROM products p " +
                    "LEFT JOIN categories c ON p.category_id = c.id " +
                    "ORDER BY p.created_at DESC";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                products.add(mapResultSetToProduct(rs));
            }
        } catch (SQLException e) {
            System.err.println("查询商品列表失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return products;
    }
    
    /**
     * 根据分类查询商品
     * @param categoryId 分类ID
     * @return 商品列表
     */
    public List<Product> findByCategory(Integer categoryId) {
        List<Product> products = new ArrayList<>();
        String sql = "SELECT p.*, c.category_name FROM products p " +
                    "LEFT JOIN categories c ON p.category_id = c.id " +
                    "WHERE p.category_id = ? ORDER BY p.product_name";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, categoryId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    products.add(mapResultSetToProduct(rs));
                }
            }
        } catch (SQLException e) {
            System.err.println("查询分类商品失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return products;
    }
    
    /**
     * 添加新商品
     * @param product 商品对象
     * @return 是否添加成功
     */
    public boolean insert(Product product) {
        String sql = "INSERT INTO products (product_code, product_name, category_id, price, discount_rate, status) " +
                    "VALUES (?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, product.getProductCode());
            stmt.setString(2, product.getProductName());
            stmt.setInt(3, product.getCategoryId());
            stmt.setBigDecimal(4, product.getPrice());
            stmt.setBigDecimal(5, product.getDiscountRate());
            stmt.setString(6, product.getStatus().name());
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("添加商品失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 更新商品信息
     * @param product 商品对象
     * @return 是否更新成功
     */
    public boolean update(Product product) {
        String sql = "UPDATE products SET product_name = ?, category_id = ?, price = ?, " +
                    "discount_rate = ?, status = ? WHERE id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, product.getProductName());
            stmt.setInt(2, product.getCategoryId());
            stmt.setBigDecimal(3, product.getPrice());
            stmt.setBigDecimal(4, product.getDiscountRate());
            stmt.setString(5, product.getStatus().name());
            stmt.setInt(6, product.getId());
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("更新商品失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 更新商品价格
     * @param productId 商品ID
     * @param price 新价格
     * @return 是否更新成功
     */
    public boolean updatePrice(Integer productId, java.math.BigDecimal price) {
        String sql = "UPDATE products SET price = ? WHERE id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setBigDecimal(1, price);
            stmt.setInt(2, productId);
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("更新商品价格失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 更新商品折扣率
     * @param productId 商品ID
     * @param discountRate 折扣率
     * @return 是否更新成功
     */
    public boolean updateDiscountRate(Integer productId, java.math.BigDecimal discountRate) {
        String sql = "UPDATE products SET discount_rate = ? WHERE id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setBigDecimal(1, discountRate);
            stmt.setInt(2, productId);
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("更新商品折扣率失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 更新商品状态
     * @param productId 商品ID
     * @param status 商品状态
     * @return 是否更新成功
     */
    public boolean updateStatus(Integer productId, Product.Status status) {
        String sql = "UPDATE products SET status = ? WHERE id = ?";
        
        try (Connection conn = DatabaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, status.name());
            stmt.setInt(2, productId);
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("更新商品状态失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 将ResultSet映射为Product对象
     * @param rs ResultSet对象
     * @return Product对象
     * @throws SQLException SQL异常
     */
    private Product mapResultSetToProduct(ResultSet rs) throws SQLException {
        Product product = new Product();
        product.setId(rs.getInt("id"));
        product.setProductCode(rs.getString("product_code"));
        product.setProductName(rs.getString("product_name"));
        product.setCategoryId(rs.getInt("category_id"));
        product.setPrice(rs.getBigDecimal("price"));
        product.setDiscountRate(rs.getBigDecimal("discount_rate"));
        product.setStatus(Product.Status.valueOf(rs.getString("status")));
        product.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
        product.setUpdatedAt(rs.getTimestamp("updated_at").toLocalDateTime());
        return product;
    }
}
